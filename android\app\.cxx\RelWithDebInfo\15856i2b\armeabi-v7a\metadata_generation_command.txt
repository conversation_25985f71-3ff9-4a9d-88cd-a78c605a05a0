                        -HD:\flutter\sdk\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\Program Files\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\Program Files\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\Program Files\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Program Files\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\ai-workspace\echo-cave\android\app\build\intermediates\cxx\RelWithDebInfo\15856i2b\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\ai-workspace\echo-cave\android\app\build\intermediates\cxx\RelWithDebInfo\15856i2b\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\ai-workspace\echo-cave\android\app\.cxx\RelWithDebInfo\15856i2b\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2