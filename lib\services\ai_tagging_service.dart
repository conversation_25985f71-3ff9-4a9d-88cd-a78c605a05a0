import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;

class AITaggingService {
  final FlutterSecureStorage _secureStorage;
  static const String _apiKeyName = 'deepseek_api_key';
  static const String _apiUrl = 'https://api.deepseek.com/v1/chat/completions';

  AITaggingService(this._secureStorage);

  Future<List<String>> getTagsForText(String text) async {
    try {
      final apiKey = await _secureStorage.read(key: _apiKeyName);
      if (apiKey == null) {
        throw Exception('API key not found');
      }

      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': 'deepseek-chat',
          'messages': [
            {
              'role': 'system',
              'content':
                  "You are an expert in analyzing text for emotional content. Your task is to extract 1 to 3 single-word emotional tags from the user's text. Respond with only a comma-separated list of lowercase words. For example: sad,anxious,hopeful"
            },
            {
              'role': 'user',
              'content': text,
            }
          ],
          'temperature': 0.2,
          'max_tokens': 20,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        final content = data['choices'][0]['message']['content'] as String;
        // Clean up the response and split into a list of tags
        return content
            .trim()
            .toLowerCase()
            .split(',')
            .map((tag) => tag.trim())
            .where((tag) => tag.isNotEmpty)
            .toList();
      } else {
        debugPrint('API Error: ${response.statusCode} ${response.body}');
        return ['error'];
      }
    } catch (e) {
      debugPrint('Exception during tagging: $e');
      return ['error'];
    }
  }
}
