import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/parallel_audio_io_service.dart';
import '../services/stt/audio_stt_bridge.dart';
import '../services/tts_service.dart';
import '../services/empathy/empathy_engine.dart';
import '../services/empathy/emotion_models.dart';

/// 共鸣引擎测试界面
class EmpathyTestView extends HookConsumerWidget {
  const EmpathyTestView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 服务实例
    final audioService = useMemoized(() => ParallelAudioIOService());
    final sttBridge = useMemoized(() => AudioSttBridge(audioService));
    final ttsService = useMemoized(() => TTSService());
    final empathyEngine = useMemoized(() => EmpathyEngine(
      sttBridge: sttBridge,
      ttsService: ttsService,
    ));
    
    // 状态管理
    final isInitialized = useState(false);
    final isRunning = useState(false);
    final emotionAnalyses = useState<List<EmotionAnalysis>>([]);
    final triggers = useState<List<EmpathyTrigger>>([]);
    final responses = useState<List<String>>([]);
    final engineStats = useState<Map<String, dynamic>>({});

    // 初始化
    useEffect(() {
      _initializeEngine(empathyEngine, isInitialized);
      
      // 监听情感分析
      final emotionSubscription = empathyEngine.onEmotionAnalysis.listen((analysis) {
        emotionAnalyses.value = [...emotionAnalyses.value, analysis];
      });
      
      // 监听触发事件
      final triggerSubscription = empathyEngine.onTrigger.listen((trigger) {
        triggers.value = [...triggers.value, trigger];
      });
      
      // 监听AI回应
      final responseSubscription = empathyEngine.onResponse.listen((response) {
        responses.value = [...responses.value, response];
      });

      return () {
        emotionSubscription.cancel();
        triggerSubscription.cancel();
        responseSubscription.cancel();
        empathyEngine.dispose();
      };
    }, []);

    // 定期更新统计信息
    useEffect(() {
      Timer? timer;
      if (isRunning.value) {
        timer = Timer.periodic(const Duration(seconds: 2), (_) {
          engineStats.value = empathyEngine.getEngineStats();
        });
      }
      return () => timer?.cancel();
    }, [isRunning.value]);

    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      appBar: AppBar(
        title: const Text('共鸣引擎测试'),
        backgroundColor: const Color(0xFF16213E),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 引擎状态卡片
            _buildEngineStatusCard(empathyEngine, isInitialized.value, engineStats.value),
            
            const SizedBox(height: 16),
            
            // 控制按钮
            _buildControlButtons(
              empathyEngine, 
              isInitialized.value, 
              isRunning,
              emotionAnalyses,
              triggers,
              responses,
            ),
            
            const SizedBox(height: 16),
            
            // 结果显示区域
            Expanded(
              child: _buildResultsDisplay(
                emotionAnalyses.value, 
                triggers.value, 
                responses.value,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 初始化引擎
  Future<void> _initializeEngine(
    EmpathyEngine empathyEngine,
    ValueNotifier<bool> isInitialized,
  ) async {
    try {
      final success = await empathyEngine.initialize();
      isInitialized.value = success;
    } catch (e) {
      debugPrint('初始化共鸣引擎失败: $e');
    }
  }

  /// 构建引擎状态卡片
  Widget _buildEngineStatusCard(
    EmpathyEngine empathyEngine, 
    bool isInitialized, 
    Map<String, dynamic> stats,
  ) {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isInitialized ? Icons.check_circle : Icons.error,
                  color: isInitialized ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                const Text(
                  '共鸣引擎状态',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 基本状态
            _buildStatusRow('初始化状态', isInitialized ? '已初始化' : '未初始化'),
            _buildStatusRow('运行状态', empathyEngine.isActive ? '运行中' : '已停止'),
            _buildStatusRow('STT状态', empathyEngine.isSttActive ? '活跃' : '未活跃'),
            _buildStatusRow('AI状态', empathyEngine.isAiGenerating ? '生成中' : '就绪'),
            
            if (stats.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Divider(color: Colors.white24),
              const SizedBox(height: 8),
              
              // 统计信息
              const Text(
                '会话统计',
                style: TextStyle(color: Colors.white70, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              
              _buildStatusRow('会话时长', '${stats['sessionDuration'] ?? 0} 分钟'),
              _buildStatusRow('情感分析', '${stats['totalAnalyses'] ?? 0} 次'),
              _buildStatusRow('触发次数', '${stats['totalTriggers'] ?? 0} 次'),
              _buildStatusRow('AI回应', '${stats['totalResponses'] ?? 0} 次'),
              _buildStatusRow('触发率', '${(stats['triggerRate'] ?? 0.0).toStringAsFixed(1)} 次/分钟'),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建状态行
  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70),
          ),
          Text(
            value,
            style: const TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButtons(
    EmpathyEngine empathyEngine,
    bool isInitialized,
    ValueNotifier<bool> isRunning,
    ValueNotifier<List<EmotionAnalysis>> emotionAnalyses,
    ValueNotifier<List<EmpathyTrigger>> triggers,
    ValueNotifier<List<String>> responses,
  ) {
    return Row(
      children: [
        // 开始按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isInitialized && !isRunning.value
                ? () => _startEngine(empathyEngine, isRunning)
                : null,
            icon: const Icon(Icons.play_arrow),
            label: const Text('开始测试'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 停止按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isRunning.value
                ? () => _stopEngine(empathyEngine, isRunning)
                : null,
            icon: const Icon(Icons.stop),
            label: const Text('停止测试'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 清除按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _clearResults(
              empathyEngine, emotionAnalyses, triggers, responses),
            icon: const Icon(Icons.clear),
            label: const Text('清除'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 开始引擎
  Future<void> _startEngine(EmpathyEngine empathyEngine, ValueNotifier<bool> isRunning) async {
    try {
      await empathyEngine.start();
      isRunning.value = true;
    } catch (e) {
      debugPrint('启动共鸣引擎失败: $e');
    }
  }

  /// 停止引擎
  Future<void> _stopEngine(EmpathyEngine empathyEngine, ValueNotifier<bool> isRunning) async {
    try {
      await empathyEngine.stop();
      isRunning.value = false;
    } catch (e) {
      debugPrint('停止共鸣引擎失败: $e');
    }
  }

  /// 清除结果
  Future<void> _clearResults(
    EmpathyEngine empathyEngine,
    ValueNotifier<List<EmotionAnalysis>> emotionAnalyses,
    ValueNotifier<List<EmpathyTrigger>> triggers,
    ValueNotifier<List<String>> responses,
  ) async {
    await empathyEngine.reset();
    emotionAnalyses.value = [];
    triggers.value = [];
    responses.value = [];
  }

  /// 构建结果显示区域
  Widget _buildResultsDisplay(
    List<EmotionAnalysis> emotions,
    List<EmpathyTrigger> triggers,
    List<String> responses,
  ) {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          const TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white54,
            indicatorColor: Colors.tealAccent,
            tabs: [
              Tab(text: '情感分析'),
              Tab(text: '触发事件'),
              Tab(text: 'AI回应'),
            ],
          ),
          
          Expanded(
            child: TabBarView(
              children: [
                // 情感分析页
                _buildEmotionsList(emotions),
                
                // 触发事件页
                _buildTriggersList(triggers),
                
                // AI回应页
                _buildResponsesList(responses),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建情感分析列表
  Widget _buildEmotionsList(List<EmotionAnalysis> emotions) {
    if (emotions.isEmpty) {
      return const Center(
        child: Text(
          '暂无情感分析结果\n开始测试后将显示实时情感分析',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: emotions.length,
      itemBuilder: (context, index) {
        final emotion = emotions[index];
        return Card(
          color: const Color(0xFF1A1A2E),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Icon(
              _getEmotionIcon(emotion.primaryEmotion),
              color: _getEmotionColor(emotion.primaryEmotion),
            ),
            title: Text(
              '${_getEmotionName(emotion.primaryEmotion)} (${emotion.intensity.name})',
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '原文: ${emotion.originalText}',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
                if (emotion.keywords.isNotEmpty)
                  Text(
                    '关键词: ${emotion.keywords.join(", ")}',
                    style: const TextStyle(color: Colors.white54, fontSize: 11),
                  ),
                Text(
                  '置信度: ${(emotion.confidence * 100).toStringAsFixed(1)}% | 分数: ${emotion.emotionScore.toStringAsFixed(2)}',
                  style: const TextStyle(color: Colors.white54, fontSize: 11),
                ),
              ],
            ),
            trailing: Text(
              emotion.timestamp.toString().substring(11, 19),
              style: const TextStyle(color: Colors.white54, fontSize: 10),
            ),
          ),
        );
      },
    );
  }

  /// 构建触发事件列表
  Widget _buildTriggersList(List<EmpathyTrigger> triggers) {
    if (triggers.isEmpty) {
      return const Center(
        child: Text(
          '暂无触发事件\n当检测到情感变化时将显示触发记录',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: triggers.length,
      itemBuilder: (context, index) {
        final trigger = triggers[index];
        return Card(
          color: const Color(0xFF2D1B1B),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Icon(
              _getTriggerIcon(trigger.type),
              color: _getTriggerColor(trigger.strength),
            ),
            title: Text(
              _getTriggerTypeName(trigger.type),
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  trigger.reason,
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
                Text(
                  '强度: ${(trigger.strength * 100).toStringAsFixed(1)}% | 建议回应: ${_getResponseTypeName(trigger.suggestedResponse)}',
                  style: const TextStyle(color: Colors.white54, fontSize: 11),
                ),
              ],
            ),
            trailing: Text(
              trigger.timestamp.toString().substring(11, 19),
              style: const TextStyle(color: Colors.white54, fontSize: 10),
            ),
          ),
        );
      },
    );
  }

  /// 构建AI回应列表
  Widget _buildResponsesList(List<String> responses) {
    if (responses.isEmpty) {
      return const Center(
        child: Text(
          '暂无AI回应\n当触发共鸣条件时AI将生成回应',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: responses.length,
      itemBuilder: (context, index) {
        final response = responses[index];
        return Card(
          color: const Color(0xFF1B2D1B),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: const Icon(Icons.chat_bubble, color: Colors.lightGreen),
            title: Text(
              response,
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Text(
              'AI回应 #${index + 1}',
              style: const TextStyle(color: Colors.white54, fontSize: 11),
            ),
          ),
        );
      },
    );
  }

  // 辅助方法
  IconData _getEmotionIcon(EmotionType emotion) {
    const icons = {
      EmotionType.sad: Icons.sentiment_very_dissatisfied,
      EmotionType.anxious: Icons.psychology,
      EmotionType.angry: Icons.sentiment_very_dissatisfied,
      EmotionType.lonely: Icons.person_outline,
      EmotionType.stressed: Icons.warning,
      EmotionType.confused: Icons.help_outline,
      EmotionType.happy: Icons.sentiment_very_satisfied,
      EmotionType.hopeful: Icons.star,
      EmotionType.neutral: Icons.sentiment_neutral,
    };
    return icons[emotion] ?? Icons.sentiment_neutral;
  }

  Color _getEmotionColor(EmotionType emotion) {
    const colors = {
      EmotionType.sad: Colors.blue,
      EmotionType.anxious: Colors.orange,
      EmotionType.angry: Colors.red,
      EmotionType.lonely: Colors.purple,
      EmotionType.stressed: Colors.deepOrange,
      EmotionType.confused: Colors.amber,
      EmotionType.happy: Colors.green,
      EmotionType.hopeful: Colors.lightGreen,
      EmotionType.neutral: Colors.grey,
    };
    return colors[emotion] ?? Colors.grey;
  }

  IconData _getTriggerIcon(EmpathyTriggerType type) {
    const icons = {
      EmpathyTriggerType.emotionalKeyword: Icons.key,
      EmpathyTriggerType.speechRateAnomaly: Icons.speed,
      EmpathyTriggerType.longPause: Icons.pause,
      EmpathyTriggerType.emotionIntensity: Icons.trending_up,
      EmpathyTriggerType.repetitiveExpression: Icons.repeat,
      EmpathyTriggerType.helpSignal: Icons.help,
      EmpathyTriggerType.emotionTransition: Icons.swap_horiz,
    };
    return icons[type] ?? Icons.notifications;
  }

  Color _getTriggerColor(double strength) {
    if (strength > 0.8) return Colors.red;
    if (strength > 0.6) return Colors.orange;
    if (strength > 0.4) return Colors.yellow;
    return Colors.green;
  }

  String _getEmotionName(EmotionType emotion) {
    const names = {
      EmotionType.sad: '悲伤',
      EmotionType.anxious: '焦虑',
      EmotionType.angry: '愤怒',
      EmotionType.lonely: '孤独',
      EmotionType.stressed: '压力',
      EmotionType.confused: '困惑',
      EmotionType.happy: '快乐',
      EmotionType.hopeful: '希望',
      EmotionType.neutral: '平静',
    };
    return names[emotion] ?? emotion.name;
  }

  String _getTriggerTypeName(EmpathyTriggerType type) {
    const names = {
      EmpathyTriggerType.emotionalKeyword: '情感关键词',
      EmpathyTriggerType.speechRateAnomaly: '语速异常',
      EmpathyTriggerType.longPause: '长时间停顿',
      EmpathyTriggerType.emotionIntensity: '情感强度',
      EmpathyTriggerType.repetitiveExpression: '重复表达',
      EmpathyTriggerType.helpSignal: '求助信号',
      EmpathyTriggerType.emotionTransition: '情感转换',
    };
    return names[type] ?? type.name;
  }

  String _getResponseTypeName(EmpathyResponseType type) {
    const names = {
      EmpathyResponseType.understanding: '理解确认',
      EmpathyResponseType.emotionalSupport: '情感支持',
      EmpathyResponseType.gentleInquiry: '温和询问',
      EmpathyResponseType.companionship: '陪伴安慰',
      EmpathyResponseType.encouragement: '积极鼓励',
      EmpathyResponseType.silentPresence: '静默陪伴',
      EmpathyResponseType.distraction: '转移注意',
    };
    return names[type] ?? type.name;
  }
}
