import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/foundation.dart';
import 'streaming_stt_service.dart';
import 'stt_models.dart';

/// STT配置服务
class SttConfigService {
  static const String _xunfeiAppIdKey = 'XUNFEI_APP_ID';
  static const String _xunfeiApiKeyKey = 'XUNFEI_API_KEY';

  /// 获取科大讯飞App ID
  static String? get xunfeiAppId => dotenv.env[_xunfeiAppIdKey];

  /// 获取科大讯飞API Key
  static String? get xunfeiApiKey => dotenv.env[_xunfeiApiKeyKey];

  /// 检查科大讯飞配置是否完整
  static bool get isXunfeiConfigured {
    return xunfeiAppId != null && 
           xunfeiApiKey != null && 
           xunfeiAppId!.isNotEmpty && 
           xunfeiApiKey!.isNotEmpty;
  }

  /// 创建默认的STT配置
  static SttConfig createDefaultConfig() {
    return const SttConfig(
      sampleRate: 16000,
      audioFormat: SttAudioFormat.pcm16,
      language: 'zh-cn',
      enableInterimResults: true,
      enablePunctuation: true,
      silenceTimeout: 5000,
      maxRecordingDuration: 60000,
    );
  }

  /// 创建科大讯飞STT服务
  static StreamingSttService? createXunfeiService() {
    if (!isXunfeiConfigured) {
      debugPrint('科大讯飞配置不完整，无法创建STT服务');
      return null;
    }

    try {
      return SttServiceFactory.createXunfeiService(
        appId: xunfeiAppId!,
        apiKey: xunfeiApiKey!,
      );
    } catch (e) {
      debugPrint('创建科大讯飞STT服务失败: $e');
      return null;
    }
  }

  /// 创建默认STT服务（优先使用科大讯飞）
  static StreamingSttService? createDefaultService() {
    // 优先尝试科大讯飞
    final xunfeiService = createXunfeiService();
    if (xunfeiService != null) {
      debugPrint('使用科大讯飞STT服务');
      return xunfeiService;
    }

    // TODO: 添加其他STT服务的fallback
    debugPrint('没有可用的STT服务');
    return null;
  }

  /// 验证配置
  static Map<String, dynamic> validateConfig() {
    final result = <String, dynamic>{
      'isValid': false,
      'errors': <String>[],
      'warnings': <String>[],
    };

    // 检查科大讯飞配置
    if (xunfeiAppId == null || xunfeiAppId!.isEmpty) {
      result['errors'].add('科大讯飞App ID未配置');
    }

    if (xunfeiApiKey == null || xunfeiApiKey!.isEmpty) {
      result['errors'].add('科大讯飞API Key未配置');
    }

    // 检查App ID格式
    if (xunfeiAppId != null && xunfeiAppId!.isNotEmpty) {
      if (xunfeiAppId!.length != 8) {
        result['warnings'].add('科大讯飞App ID长度异常，通常为8位');
      }
    }

    // 检查API Key格式
    if (xunfeiApiKey != null && xunfeiApiKey!.isNotEmpty) {
      if (xunfeiApiKey!.length != 32) {
        result['warnings'].add('科大讯飞API Key长度异常，通常为32位');
      }
    }

    result['isValid'] = (result['errors'] as List).isEmpty;
    return result;
  }

  /// 获取配置信息摘要
  static Map<String, String> getConfigSummary() {
    return {
      '科大讯飞App ID': xunfeiAppId != null && xunfeiAppId!.isNotEmpty 
          ? '${xunfeiAppId!.substring(0, 4)}****' 
          : '未配置',
      '科大讯飞API Key': xunfeiApiKey != null && xunfeiApiKey!.isNotEmpty 
          ? '${xunfeiApiKey!.substring(0, 8)}****' 
          : '未配置',
      '配置状态': isXunfeiConfigured ? '已配置' : '未配置',
    };
  }
}
