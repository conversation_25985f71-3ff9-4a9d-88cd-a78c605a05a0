import 'dart:async';
import 'package:flutter/foundation.dart';
import '../parallel_audio_io_service.dart';
import 'streaming_stt_service.dart';
import 'stt_models.dart';
import 'stt_config_service.dart';

/// 音频STT桥接服务
/// 
/// 负责将AEC音频系统的音频流连接到STT服务，实现实时语音转文字
class AudioSttBridge extends ChangeNotifier {
  // 服务依赖
  final ParallelAudioIOService _audioService;
  StreamingSttService? _sttService;
  
  // 状态管理
  bool _isActive = false;
  bool _isInitialized = false;
  StreamSubscription<Uint8List>? _audioSubscription;
  
  // 事件流控制器
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  
  // 配置
  SttConfig _config = SttConfigService.createDefaultConfig();
  
  // 统计信息
  int _totalAudioBytes = 0;
  DateTime? _sessionStartTime;

  AudioSttBridge(this._audioService);

  /// 当前状态
  bool get isActive => _isActive;
  bool get isInitialized => _isInitialized;
  bool get hasAudioService => _audioService.isInitialized;
  bool get hasSttService => _sttService != null;
  
  /// STT连接状态
  SttConnectionState get sttConnectionState => 
      _sttService?.connectionState ?? SttConnectionState.disconnected;
  
  /// 音频服务状态
  bool get audioServiceRecording => _audioService.isRecording;
  
  /// 配置信息
  SttConfig get config => _config;
  
  /// 统计信息
  Map<String, dynamic> get stats => {
    'isActive': _isActive,
    'totalAudioBytes': _totalAudioBytes,
    'sessionDuration': _sessionStartTime != null 
        ? DateTime.now().difference(_sessionStartTime!).inMilliseconds 
        : 0,
    'sttStats': _sttService?.stats.toString() ?? 'N/A',
  };

  /// 识别结果流
  Stream<SttResult> get onResult => _resultController.stream;
  
  /// 错误事件流
  Stream<SttError> get onError => _errorController.stream;

  /// 初始化桥接服务
  Future<bool> initialize({SttConfig? config}) async {
    try {
      if (config != null) {
        _config = config;
      }

      // 确保音频服务已初始化
      if (!_audioService.isInitialized) {
        final audioInitialized = await _audioService.initialize();
        if (!audioInitialized) {
          debugPrint('音频服务初始化失败');
          return false;
        }
      }

      // 创建STT服务
      _sttService = SttConfigService.createDefaultService();
      if (_sttService == null) {
        debugPrint('无法创建STT服务，请检查配置');
        return false;
      }

      // 初始化STT服务
      final sttInitialized = await _sttService!.initialize(_config);
      if (!sttInitialized) {
        debugPrint('STT服务初始化失败');
        return false;
      }

      // 监听STT事件
      _sttService!.onResult.listen(_onSttResult);
      _sttService!.onError.listen(_onSttError);

      _isInitialized = true;
      notifyListeners();
      
      debugPrint('音频STT桥接服务初始化成功');
      return true;

    } catch (e) {
      debugPrint('音频STT桥接服务初始化失败: $e');
      return false;
    }
  }

  /// 开始实时语音识别
  Future<void> startRecognition() async {
    if (!_isInitialized || _isActive) {
      debugPrint('桥接服务未初始化或已在运行中');
      return;
    }

    try {
      // 启动STT监听
      await _sttService!.startListening();
      
      // 启动音频流录制
      await _audioService.startStreamRecording();
      
      // 订阅音频流
      _audioSubscription = _audioService.audioStream?.listen(
        _onAudioData,
        onError: _onAudioError,
        onDone: _onAudioDone,
      );

      _isActive = true;
      _sessionStartTime = DateTime.now();
      _totalAudioBytes = 0;
      
      notifyListeners();
      debugPrint('开始实时语音识别');

    } catch (e) {
      debugPrint('启动语音识别失败: $e');
      _emitError(SttErrorType.unknown, 'START_FAILED', '启动识别失败: $e', e);
      rethrow;
    }
  }

  /// 停止实时语音识别
  Future<void> stopRecognition() async {
    if (!_isActive) return;

    try {
      // 停止音频流订阅
      await _audioSubscription?.cancel();
      _audioSubscription = null;

      // 停止STT监听
      await _sttService?.stopListening();

      // 停止音频录制
      await _audioService.stopRecording();

      _isActive = false;
      notifyListeners();
      
      debugPrint('停止实时语音识别');

    } catch (e) {
      debugPrint('停止语音识别失败: $e');
      _emitError(SttErrorType.unknown, 'STOP_FAILED', '停止识别失败: $e', e);
    }
  }

  /// 重置服务状态
  Future<void> reset() async {
    await stopRecognition();
    
    _totalAudioBytes = 0;
    _sessionStartTime = null;
    
    await _sttService?.reset();
    
    debugPrint('音频STT桥接服务已重置');
    notifyListeners();
  }

  /// 更新配置
  Future<void> updateConfig(SttConfig newConfig) async {
    _config = newConfig;
    
    if (_sttService != null) {
      await _sttService!.reset();
      await _sttService!.initialize(_config);
    }
    
    notifyListeners();
    debugPrint('STT配置已更新');
  }

  /// 获取配置验证结果
  Map<String, dynamic> validateConfiguration() {
    return SttConfigService.validateConfig();
  }

  /// 获取配置摘要
  Map<String, String> getConfigSummary() {
    final sttConfig = SttConfigService.getConfigSummary();
    final audioConfig = {
      '音频服务状态': _audioService.isInitialized ? '已初始化' : '未初始化',
      '录音状态': _audioService.isRecording ? '录音中' : '未录音',
    };
    
    return {...sttConfig, ...audioConfig};
  }

  /// 处理音频数据
  void _onAudioData(Uint8List audioData) {
    if (!_isActive || _sttService == null) return;

    try {
      // 发送音频数据到STT服务
      _sttService!.sendAudioData(audioData);
      
      // 更新统计信息
      _totalAudioBytes += audioData.length;
      
    } catch (e) {
      debugPrint('处理音频数据失败: $e');
      _emitError(SttErrorType.audioFormat, 'AUDIO_PROCESS_FAILED', '音频处理失败: $e', e);
    }
  }

  /// 处理音频错误
  void _onAudioError(error) {
    debugPrint('音频流错误: $error');
    _emitError(SttErrorType.audioFormat, 'AUDIO_STREAM_ERROR', '音频流错误: $error', error);
  }

  /// 音频流结束
  void _onAudioDone() {
    debugPrint('音频流结束');
    stopRecognition();
  }

  /// 处理STT识别结果
  void _onSttResult(SttResult result) {
    _resultController.add(result);
    debugPrint('STT识别结果: ${result.text} (最终: ${result.isFinal})');
  }

  /// 处理STT错误
  void _onSttError(SttError error) {
    _errorController.add(error);
    debugPrint('STT错误: ${error.message}');
  }

  /// 发送错误事件
  void _emitError(SttErrorType type, String code, String message, [Object? originalError]) {
    final error = SttError(
      code: code,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      originalError: originalError,
    );
    
    _errorController.add(error);
  }

  @override
  void dispose() {
    stopRecognition();
    _resultController.close();
    _errorController.close();
    _sttService?.dispose();
    super.dispose();
  }
}
