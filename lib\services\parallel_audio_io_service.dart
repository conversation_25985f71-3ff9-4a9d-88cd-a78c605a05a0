import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_sound/flutter_sound.dart' as fs;
import 'package:just_audio/just_audio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

/// AEC测试模式枚举
enum AECTestMode {
  flutterSoundSeparate, // 分离模式
  flutterSoundSimultaneous, // 同时模式
  flutterSoundWithAEC, // iOS AEC模式
  mixedEngines, // 混合引擎
  streamBased, // 流式处理
}

/// AEC测试结果数据类
class AECTestResult {
  final AECTestMode mode;
  final DateTime startTime;
  final Duration testDuration;
  DateTime? endTime;
  Duration? actualDuration;
  bool success = false;
  bool? simultaneousSuccess;
  String? errorMessage;
  String notes = '';
  String? recordingPath;

  AECTestResult({
    required this.mode,
    required this.startTime,
    required this.testDuration,
  });
}

/// 并行音频I/O服务
/// 专门用于验证同时录音播放和回声消除(AEC)的技术原型
class ParallelAudioIOService extends ChangeNotifier {
  // Flutter Sound 组件
  fs.FlutterSoundRecorder? _recorder;
  fs.FlutterSoundPlayer? _player;

  // Just Audio 播放器（用于测试分离引擎）
  AudioPlayer? _justAudioPlayer;

  // 状态管理
  bool _isRecorderInitialized = false;
  bool _isPlayerInitialized = false;
  bool _isRecording = false;
  bool _isPlaying = false;
  bool _isSimultaneousMode = false;

  // 公共状态访问器
  bool get isInitialized => _isRecorderInitialized && _isPlayerInitialized;
  bool get isRecording => _isRecording;
  bool get isPlaying => _isPlaying;
  bool get isSimultaneousMode => _isSimultaneousMode;

  // 音频数据流
  StreamController<Uint8List>? _audioStreamController;
  Stream<Uint8List>? _audioStream;
  StreamSubscription? _streamSubscription;

  // 配置
  // AECTestMode _currentMode = AECTestMode.flutterSoundSeparate; // 当前未使用
  String? _currentRecordingPath;

  // 测试结果
  final List<AECTestResult> _testResults = [];

  // Getters
  List<AECTestResult> get testResults => List.unmodifiable(_testResults);
  Stream<fs.RecordingDisposition>? get onProgress => _recorder?.onProgress;

  /// 初始化服务
  Future<bool> initialize() async {
    try {
      // 检查权限
      final micPermission = await Permission.microphone.request();
      if (micPermission != PermissionStatus.granted) {
        throw Exception('麦克风权限未授予');
      }

      // 初始化FlutterSound录音器
      await _initializeRecorder();

      // 初始化FlutterSound播放器
      await _initializePlayer();

      // 初始化JustAudio播放器
      await _initializeJustAudio();

      debugPrint('并行音频I/O服务初始化成功');
      return true;
    } catch (e) {
      debugPrint('并行音频I/O服务初始化失败: $e');
      return false;
    }
  }

  /// 初始化FlutterSound录音器
  Future<void> _initializeRecorder() async {
    if (_isRecorderInitialized) return;

    _recorder = fs.FlutterSoundRecorder();
    await _recorder!.openRecorder();
    _isRecorderInitialized = true;
    notifyListeners();
  }

  /// 初始化FlutterSound播放器
  Future<void> _initializePlayer() async {
    if (_isPlayerInitialized) return;

    _player = fs.FlutterSoundPlayer();
    await _player!.openPlayer();
    _isPlayerInitialized = true;
    notifyListeners();
  }

  /// 初始化JustAudio播放器
  Future<void> _initializeJustAudio() async {
    _justAudioPlayer = AudioPlayer();
  }

  /// 开始AEC测试
  Future<AECTestResult> startAECTest(
    AECTestMode mode, {
    String? testAudioPath,
    Duration testDuration = const Duration(seconds: 8), // 优化：缩短默认测试时间
  }) async {
    debugPrint('开始AEC测试，模式: $mode');

    final testResult = AECTestResult(
      mode: mode,
      startTime: DateTime.now(),
      testDuration: testDuration,
    );

    try {
      // _currentMode = mode; // 当前未使用

      switch (mode) {
        case AECTestMode.flutterSoundSeparate:
          await _testFlutterSoundSeparate(
              testResult, testAudioPath, testDuration);
          break;
        case AECTestMode.flutterSoundSimultaneous:
          await _testFlutterSoundSimultaneous(
              testResult, testAudioPath, testDuration);
          break;
        case AECTestMode.flutterSoundWithAEC:
          await _testFlutterSoundWithAEC(
              testResult, testAudioPath, testDuration);
          break;
        case AECTestMode.mixedEngines:
          await _testMixedEngines(testResult, testAudioPath, testDuration);
          break;
        case AECTestMode.streamBased:
          await _testStreamBased(testResult, testDuration);
          break;
      }

      testResult.success = true;
      debugPrint('AEC测试完成: ${testResult.mode}');
    } catch (e) {
      testResult.success = false;
      testResult.errorMessage = e.toString();
      debugPrint('AEC测试失败: $e');
    } finally {
      testResult.endTime = DateTime.now();
      if (testResult.endTime != null) {
        testResult.actualDuration =
            testResult.endTime!.difference(testResult.startTime);
      }
      _testResults.add(testResult);
      notifyListeners();
    }

    return testResult;
  }

  /// 测试模式1: FlutterSound分离模式
  Future<void> _testFlutterSoundSeparate(AECTestResult result,
      String? testAudioPath, Duration testDuration) async {
    result.notes = '使用FlutterSound，先录音后播放（分离模式）';

    // 先录音
    await startRecording();
    await Future.delayed(testDuration);
    final recordingPath = await stopRecording();

    if (recordingPath != null) {
      result.recordingPath = recordingPath; // 保存录音路径
      // 再播放录音
      await startPlayback(recordingPath);
      await Future.delayed(const Duration(seconds: 3));
      await stopPlayback();
    }
  }

  /// 测试模式2: FlutterSound同时录音播放
  Future<void> _testFlutterSoundSimultaneous(AECTestResult result,
      String? testAudioPath, Duration testDuration) async {
    _isSimultaneousMode = true;
    notifyListeners();
    try {
      result.notes = '使用FlutterSound，同时录音和播放（测试冲突）';
      await startRecording();
      await Future.delayed(const Duration(seconds: 1));
      if (testAudioPath != null) {
        try {
          await startPlayback(testAudioPath);
          result.simultaneousSuccess = true;
        } catch (e) {
          result.simultaneousSuccess = false;
          result.errorMessage = '同时播放失败: $e';
        }
      }
      await Future.delayed(testDuration);
      final recordingPath = await stopRecording();
      if (recordingPath != null) {
        result.recordingPath = recordingPath; // 保存录音路径
      }
      await stopPlayback();
    } finally {
      _isSimultaneousMode = false;
      notifyListeners();
    }
  }

  /// 测试模式3: FlutterSound启用AEC
  Future<void> _testFlutterSoundWithAEC(AECTestResult result,
      String? testAudioPath, Duration testDuration) async {
    result.notes = '使用FlutterSound，启用iOS enableVoiceProcessing（仅iOS）';

    if (Platform.isIOS) {
      // iOS特定的AEC设置
      await startRecordingWithAEC();
    } else {
      result.notes += ' - Android平台不支持此功能';
      await startRecording();
    }

    // 延迟后开始播放
    await Future.delayed(const Duration(seconds: 1));
    if (testAudioPath != null) {
      await startPlayback(testAudioPath);
    }

    await Future.delayed(testDuration);
    final recordingPath = await stopRecording();
    if (recordingPath != null) {
      result.recordingPath = recordingPath; // 保存录音路径
    }
    await stopPlayback();
  }

  /// 测试模式4: 混合引擎
  Future<void> _testMixedEngines(AECTestResult result, String? testAudioPath,
      Duration testDuration) async {
    _isSimultaneousMode = true;
    notifyListeners();
    try {
      result.notes = '混合引擎：FlutterSound录音 + JustAudio播放';
      await startRecording();
      await Future.delayed(const Duration(seconds: 1));
      if (testAudioPath != null && _justAudioPlayer != null) {
        try {
          await _justAudioPlayer!.setAsset(testAudioPath);
          await _justAudioPlayer!.play();
          result.simultaneousSuccess = true;
        } catch (e) {
          result.simultaneousSuccess = false;
          result.errorMessage = 'JustAudio播放失败: $e';
        }
      }
      await Future.delayed(testDuration);
      final recordingPath = await stopRecording();
      if (recordingPath != null) {
        result.recordingPath = recordingPath; // 保存录音路径
      }
      if (_justAudioPlayer != null) {
        await _justAudioPlayer!.stop();
      }
    } finally {
      _isSimultaneousMode = false;
      notifyListeners();
    }
  }

  /// 测试模式5: 流式处理
  Future<void> _testStreamBased(
      AECTestResult result, Duration testDuration) async {
    _isSimultaneousMode = true;
    notifyListeners();
    try {
      result.notes = '流式处理模式：实时音频数据处理';
      await startStreamRecording();
      await Future.delayed(testDuration);
      await stopStreamRecording();
    } finally {
      _isSimultaneousMode = false;
      notifyListeners();
    }
  }

  /// 开始录音
  Future<void> startRecording() async {
    if (!_isRecorderInitialized || _isRecording) return;

    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentRecordingPath = '${directory.path}/test_recording_$timestamp.m4a';

      await _recorder!.startRecorder(
        toFile: _currentRecordingPath,
        codec: fs.Codec.aacMP4,
      );

      _isRecording = true;
      notifyListeners();
      debugPrint('开始录音: $_currentRecordingPath');
    } catch (e) {
      debugPrint('录音启动失败: $e');
      rethrow;
    }
  }

  /// 开始录音（启用AEC）
  Future<void> startRecordingWithAEC() async {
    if (!_isRecorderInitialized || _isRecording) return;

    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentRecordingPath =
          '${directory.path}/test_recording_aec_$timestamp.m4a';

      // iOS特定的AEC设置
      if (Platform.isIOS) {
        // 在iOS上启用语音处理（包括AEC）
        await _recorder!.startRecorder(
          toFile: _currentRecordingPath,
          codec: fs.Codec.aacMP4,
          audioSource: fs.AudioSource.microphone,
          enableVoiceProcessing: true, // 关键：启用语音处理，包括AEC
        );
        debugPrint('开始AEC录音 (iOS enableVoiceProcessing): $_currentRecordingPath');
      } else {
        // Android平台使用标准录音
        await _recorder!.startRecorder(
          toFile: _currentRecordingPath,
          codec: fs.Codec.aacMP4,
          audioSource: fs.AudioSource.microphone,
        );
        debugPrint('开始标准录音 (Android): $_currentRecordingPath');
      }

      _isRecording = true;
      notifyListeners();
    } catch (e) {
      debugPrint('AEC录音启动失败: $e');
      rethrow;
    }
  }

  /// 停止录音
  Future<String?> stopRecording() async {
    if (!_isRecording) return null;

    try {
      await _recorder!.stopRecorder();
      _isRecording = false;
      notifyListeners();
      debugPrint('录音已停止: $_currentRecordingPath');
      return _currentRecordingPath;
    } catch (e) {
      debugPrint('录音停止失败: $e');
      rethrow;
    }
  }

  /// 开始播放
  Future<void> startPlayback(String filePath) async {
    if (!_isPlayerInitialized || _isPlaying) return;

    try {
      await _player!.startPlayer(
        fromURI: filePath,
        codec: fs.Codec.aacMP4,
      );

      _isPlaying = true;
      notifyListeners();
      debugPrint('开始播放: $filePath');
    } catch (e) {
      debugPrint('播放启动失败: $e');
      rethrow;
    }
  }

  /// 停止播放
  Future<void> stopPlayback() async {
    if (!_isPlaying) return;

    try {
      await _player!.stopPlayer();
      _isPlaying = false;
      notifyListeners();
      debugPrint('播放已停止');
    } catch (e) {
      debugPrint('播放停止失败: $e');
      rethrow;
    }
  }

  /// 播放指定的录音文件（用于回放验证）
  Future<void> playRecordingFile(String filePath) async {
    if (!_isPlayerInitialized) {
      await initialize();
    }

    try {
      await _player!.startPlayer(
        fromURI: filePath,
        codec: fs.Codec.aacMP4,
      );
      _isPlaying = true;
      notifyListeners();
      debugPrint('开始播放录音文件: $filePath');
    } catch (e) {
      debugPrint('播放录音文件失败: $e');
      rethrow;
    }
  }

  /// 检查录音文件是否存在
  Future<bool> recordingFileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      debugPrint('检查文件存在性失败: $e');
      return false;
    }
  }

  /// 开始流式录音
  Future<void> startStreamRecording() async {
    if (!_isRecorderInitialized || _isRecording) return;

    try {
      _audioStreamController = StreamController<Uint8List>();
      _audioStream = _audioStreamController!.stream;

      await _recorder!.startRecorder(
        toStream: _audioStreamController!.sink,
        codec: fs.Codec.pcm16,
        sampleRate: 16000,
      );

      // 监听音频流
      _streamSubscription = _audioStream!.listen(
        (audioData) {
          // 这里可以进行实时音频处理
          debugPrint('收到音频数据: ${audioData.length} bytes');
        },
        onError: (error) {
          debugPrint('音频流错误: $error');
        },
      );

      _isRecording = true;
      notifyListeners();
      debugPrint('开始流式录音');
    } catch (e) {
      debugPrint('流式录音启动失败: $e');
      rethrow;
    }
  }

  /// 停止流式录音
  Future<void> stopStreamRecording() async {
    if (!_isRecording) return;

    try {
      await _recorder!.stopRecorder();
      await _streamSubscription?.cancel();
      await _audioStreamController?.close();

      _streamSubscription = null;
      _audioStreamController = null;
      _audioStream = null;
      _isRecording = false;
      notifyListeners();
      debugPrint('流式录音已停止');
    } catch (e) {
      debugPrint('流式录音停止失败: $e');
      rethrow;
    }
  }

  /// 添加测试结果
  void addTestResult(AECTestResult result) {
    _testResults.add(result);
    notifyListeners();
  }

  /// 清除测试结果
  void clearTestResults() {
    _testResults.clear();
    notifyListeners();
    debugPrint('测试结果已清除');
  }

  /// 生成测试报告
  String generateTestReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== AEC技术验证报告 ===');
    buffer.writeln('生成时间: ${DateTime.now()}');
    buffer.writeln('测试总数: ${_testResults.length}');
    buffer.writeln('');

    for (int i = 0; i < _testResults.length; i++) {
      final result = _testResults[i];
      buffer.writeln('测试 ${i + 1}:');
      buffer.writeln('  模式: ${result.mode}');
      buffer.writeln('  成功: ${result.success}');
      buffer.writeln('  持续时间: ${result.actualDuration?.inSeconds ?? 0}秒');

      if (result.simultaneousSuccess != null) {
        buffer.writeln('  同时播放: ${result.simultaneousSuccess! ? "成功" : "失败"}');
      }

      if (result.errorMessage != null) {
        buffer.writeln('  错误: ${result.errorMessage}');
      }

      if (result.notes.isNotEmpty) {
        buffer.writeln('  备注: ${result.notes}');
      }

      buffer.writeln('');
    }

    final successCount = _testResults.where((r) => r.success).length;
    buffer.writeln(
        '成功率: ${_testResults.isEmpty ? 0 : (successCount / _testResults.length * 100).toStringAsFixed(1)}%');

    return buffer.toString();
  }

  /// AEC效果对比测试
  /// 连续进行两次测试：一次不启用AEC，一次启用AEC
  Future<List<AECTestResult>> runAECComparisonTest({
    String? testAudioPath,
    Duration testDuration = const Duration(seconds: 10),
  }) async {
    final results = <AECTestResult>[];

    // 测试1: 不启用AEC的标准录音
    final standardTest = AECTestResult(
      mode: AECTestMode.flutterSoundSimultaneous,
      startTime: DateTime.now(),
      testDuration: testDuration,
    );
    standardTest.notes = '对比测试 - 标准录音（无AEC）';

    try {
      await _testFlutterSoundSimultaneous(standardTest, testAudioPath, testDuration);
      standardTest.success = true;
    } catch (e) {
      standardTest.success = false;
      standardTest.errorMessage = e.toString();
    }
    results.add(standardTest);

    // 等待一段时间再进行下一个测试
    await Future.delayed(const Duration(seconds: 2));

    // 测试2: 启用AEC的录音
    final aecTest = AECTestResult(
      mode: AECTestMode.flutterSoundWithAEC,
      startTime: DateTime.now(),
      testDuration: testDuration,
    );
    aecTest.notes = '对比测试 - AEC录音';

    try {
      await _testFlutterSoundWithAEC(aecTest, testAudioPath, testDuration);
      aecTest.success = true;
    } catch (e) {
      aecTest.success = false;
      aecTest.errorMessage = e.toString();
    }
    results.add(aecTest);

    return results;
  }

  @override
  void dispose() {
    _recorder?.closeRecorder();
    _player?.closePlayer();
    _justAudioPlayer?.dispose();
    _streamSubscription?.cancel();
    _audioStreamController?.close();
    super.dispose();
  }
}
