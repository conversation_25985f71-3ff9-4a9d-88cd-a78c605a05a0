import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/parallel_audio_io_service.dart';

void main() {
  group('AEC Service Tests', () {
    late ParallelAudioIOService aecService;

    setUp(() {
      aecService = ParallelAudioIOService();
    });

    tearDown(() {
      aecService.dispose();
    });

    test('should initialize service successfully', () async {
      final result = await aecService.initialize();
      expect(result, isTrue);
      expect(aecService.isInitialized, isTrue);
    });

    test('should have correct test modes', () {
      final modes = AECTestMode.values;
      expect(modes.length, equals(5));
      expect(modes, contains(AECTestMode.flutterSoundSeparate));
      expect(modes, contains(AECTestMode.flutterSoundSimultaneous));
      expect(modes, contains(AECTestMode.flutterSoundWithAEC));
      expect(modes, contains(AECTestMode.mixedEngines));
      expect(modes, contains(AECTestMode.streamBased));
    });

    test('should create test result with correct properties', () {
      final testResult = AECTestResult(
        mode: AECTestMode.flutterSoundWithAEC,
        startTime: DateTime.now(),
        testDuration: const Duration(seconds: 10),
      );

      expect(testResult.mode, equals(AECTestMode.flutterSoundWithAEC));
      expect(testResult.testDuration, equals(const Duration(seconds: 10)));
      expect(testResult.success, isFalse); // Default value
      expect(testResult.recordingPath, isNull);
    });

    test('should generate test report', () {
      // Add some mock test results
      final result1 = AECTestResult(
        mode: AECTestMode.flutterSoundWithAEC,
        startTime: DateTime.now(),
        testDuration: const Duration(seconds: 5),
      );
      result1.success = true;
      result1.notes = 'Test successful';

      final result2 = AECTestResult(
        mode: AECTestMode.flutterSoundSimultaneous,
        startTime: DateTime.now(),
        testDuration: const Duration(seconds: 5),
      );
      result2.success = false;
      result2.errorMessage = 'Test failed';

      aecService.testResults.addAll([result1, result2]);

      final report = aecService.generateTestReport();
      expect(report, isNotEmpty);
      expect(report, contains('AEC技术验证报告'));
      expect(report, contains('成功率: 50.0%'));
      expect(report, contains('Test successful'));
      expect(report, contains('Test failed'));
    });
  });
}
