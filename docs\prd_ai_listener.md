# PRD: Echo Cave - AI 倾听者模式

## 1. 功能概述 (Feature Overview)

在 Echo Cave 现有匿名语音记录功能的基础上，新增一个 **"AI 倾听者"** 模式。此模式旨在为用户提供一个由 AI 驱动的、可进行实时语音对话的虚拟陪伴者。用户可以选择与这个 AI 倾听者进行对话，从而获得一个更具互动性的情绪释放渠道。

## 2. 目标与价值 (Objective & Value)

*   **核心目标**: 解决部分用户在单纯记录之外，希望有"对象"可以倾诉的深层需求。
*   **用户价值**:
    *   **增强陪伴感**: AI 作为一个永远在线、绝对保密、无条件接纳的倾听者，减轻用户的孤独感。
    *   **提升互动性**: 从单向的记录变为双向的交流，提供更丰富的互动体验。
    *   **保留选择权**: 用户可以自由选择"安静记录"或"AI 陪伴"，满足不同场景下的不同需求。
*   **产品价值**: 巩固产品"安全、私密的情绪港湾"的核心定位，探索 AI 情感陪伴的可能性，构建产品独特的护城河。

## 3. 功能需求 (Functional Requirements)

### FR1: 双模式入口

用户在主界面点击核心交互元素（树洞）后，应弹出一个选择面板，提供两种模式选项：

*   **🎤 快速记录 (Quick Record)**: 即现有功能，直接进入录音流程。
*   **🤖 AI 陪伴 (AI Companion)**: 进入全新的 AI 对话界面。

### FR2: AI 对话界面

该界面是 AI 倾听者功能的核心，需要包含以下元素：

*   **对话流**: 以聊天气泡的形式展示用户与 AI 的对话历史。
*   **语音输入**: 提供一个易于操作的"按住说话"或点击录音的按钮。
*   **实时反馈**:
    *   用户说话时，应有声波纹等视觉反馈。
    *   AI 正在"思考"时，应有等待动画。
*   **AI 回应**: AI 的回复应以语音形式自动播放，并同时在界面上显示文字内容。
*   **会话控制**: 提供明确的"结束对话"按钮。

### FR3: 核心 AI 对话流

系统需要实现一个完整的实时对话处理管线：

1.  **语音转文字 (STT)**: 将用户的语音输入实时或在结束后快速转换为文本。
2.  **AI 大语言模型处理**: 将转换后的文本发送给 AI 模型（如 DeepSeek API），获取富有同理心和支持性的文本回复。
3.  **文字转语音 (TTS)**: 将 AI 生成的文本回复转换为自然流畅的语音并播放给用户。

### FR4: 数据模型与存储

*   **扩展 `Recording` 模型**:
    *   新增 `type` 字段，用于区分是 `PURE_RECORDING` 还是 `AI_CONVERSATION`。
    *   为 AI 对话记录关联一个独立的对话历史。
*   **新增 `Conversation` 模型**: 用于存储单次对话的完整历史记录（包括用户和 AI 的每一条消息、时间戳等）。
*   **本地安全存储**: 所有对话历史必须像录音文件一样，在用户本地设备上进行加密存储。

### FR5: AI 人格设定

AI 倾听者应被设定为：

*   **共情与非评判**: 核心任务是倾听、理解和表达共情，不应给出强硬的建议或进行任何形式的评判。
*   **专注与支持**: 引导对话围绕用户的情绪和感受，提供支持性的话语。
*   **记忆短期化**: 为保护隐私和简化实现，AI 的记忆仅限于当前单次对话，每次都是全新的开始。

## 4. 技术方案初步建议

*   **服务层**: 创建一个新的 `AIListenerService` 来封装所有与 AI 对话相关的功能。
*   **UI 层**:
    *   修改 `MainView` 以实现模式选择弹窗。
    *   创建全新的 `AIConversationView` Widget 来承载对话界面。
*   **第三方服务/库**:
    *   **STT**: 可继续沿用或寻找更优的 Flutter 语音识别库。
    *   **LLM**: 扩展现有 `AITaggingService` 的逻辑，调用 DeepSeek 的对话 API。
    *   **TTS**: 需要引入一个新的库（如 `flutter_tts`）来实现文字转语音功能。

## 5. 里程碑规划 (Roadmap)

1.  **Phase 1: 基础架构搭建** - 建立支持新功能的数据模型、服务框架和UI入口。
2.  **Phase 2: 核心功能实现** - 对接 STT、LLM、TTS 服务，跑通完整的对话流程。
3.  **Phase 3: 体验优化与打磨** - 优化对话流畅度、完善 AI 人格、打磨 UI 细节。 