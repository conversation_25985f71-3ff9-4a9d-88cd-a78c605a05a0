{"buildFiles": ["D:\\flutter\\sdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Program Files\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ai-workspace\\echo-cave\\android\\app\\.cxx\\Debug\\3m6g3o5i\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\Program Files\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ai-workspace\\echo-cave\\android\\app\\.cxx\\Debug\\3m6g3o5i\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Program Files\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Program Files\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}