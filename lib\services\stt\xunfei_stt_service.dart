import 'dart:async';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'streaming_stt_service.dart';
import 'stt_models.dart';

/// 科大讯飞实时语音转写服务
class XunfeiSttService extends StreamingSttService {
  // 科大讯飞WebSocket API地址
  static const String _baseUrl = 'wss://rtasr.xfyun.cn/v1/ws';
  
  // 配置信息
  final String _appId;
  final String _apiKey;
  late SttConfig _config;
  
  // 连接管理
  WebSocketChannel? _channel;
  SttConnectionState _connectionState = SttConnectionState.disconnected;
  
  // 事件流控制器
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  final StreamController<SttConnectionState> _stateController = StreamController<SttConnectionState>.broadcast();
  final StreamController<void> _endOfSpeechController = StreamController<void>.broadcast();
  
  // 统计信息
  SttStats _stats = const SttStats(
    totalDuration: 0,
    totalCharacters: 0,
    averageConfidence: 0.0,
    networkLatency: 0,
    errorCount: 0,
  );
  
  // 内部状态
  bool _isListening = false;
  DateTime? _sessionStartTime;
  int _totalCharacters = 0;
  double _totalConfidence = 0.0;
  int _resultCount = 0;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 3;
  int _errorCount = 0;

  XunfeiSttService({
    required String appId,
    required String apiKey,
  }) : _appId = appId,
       _apiKey = apiKey;

  @override
  SttConnectionState get connectionState => _connectionState;

  @override
  SttConfig get config => _config;

  @override
  SttStats get stats => _stats;

  @override
  bool get isListening => _isListening;

  @override
  Stream<SttResult> get onResult => _resultController.stream;

  @override
  Stream<SttError> get onError => _errorController.stream;

  @override
  Stream<SttConnectionState> get onConnectionStateChanged => _stateController.stream;

  @override
  Stream<void> get onEndOfSpeech => _endOfSpeechController.stream;

  @override
  Future<bool> initialize(SttConfig config) async {
    try {
      _config = config;
      _updateConnectionState(SttConnectionState.disconnected);
      debugPrint('科大讯飞STT服务初始化完成');
      return true;
    } catch (e) {
      debugPrint('科大讯飞STT服务初始化失败: $e');
      _emitError(SttErrorType.configuration, 'INIT_FAILED', '初始化失败: $e', e);
      return false;
    }
  }

  @override
  Future<void> startListening() async {
    if (_connectionState == SttConnectionState.listening) {
      debugPrint('STT服务已在监听中');
      return;
    }

    try {
      _updateConnectionState(SttConnectionState.connecting);
      
      // 生成认证URL
      final wsUrl = _generateAuthUrl();
      debugPrint('连接科大讯飞STT服务: $wsUrl');
      debugPrint('网络连接测试 - 开始建立WebSocket连接...');
      
      // 建立WebSocket连接（增加超时控制）
      _channel = WebSocketChannel.connect(
        Uri.parse(wsUrl),
        protocols: null,
      );

      // 监听连接状态和消息
      _channel!.stream.listen(
        _onMessage,
        onError: _onWebSocketError,
        onDone: _onWebSocketClosed,
      );

      // 等待连接建立（增加超时检测）
      debugPrint('网络连接测试 - 等待WebSocket握手完成...');
      await Future.delayed(const Duration(milliseconds: 1000));

      _updateConnectionState(SttConnectionState.connected);
      _isListening = true;
      _sessionStartTime = DateTime.now();
      _reconnectAttempts = 0; // 连接成功时重置重连计数

      debugPrint('✅ 科大讯飞STT服务连接成功！');
      debugPrint('网络连接测试 - WebSocket连接已建立，开始监听...');
      
    } catch (e) {
      debugPrint('❌ 启动STT监听失败: $e');
      debugPrint('网络连接测试 - 连接失败详情: $e');

      // 详细的错误分析
      String errorDetail = '';
      if (e.toString().contains('10110')) {
        errorDetail = '错误码10110 - 网络连接超时或服务器无法访问';
      } else if (e.toString().contains('SocketException')) {
        errorDetail = 'Socket连接异常 - 请检查网络连接';
      } else if (e.toString().contains('HandshakeException')) {
        errorDetail = 'SSL握手失败 - 可能是网络安全设置问题';
      } else if (e.toString().contains('TimeoutException')) {
        errorDetail = '连接超时 - 网络速度可能过慢';
      } else {
        errorDetail = '未知网络错误';
      }

      debugPrint('网络诊断结果: $errorDetail');

      _updateConnectionState(SttConnectionState.error);
      _emitError(SttErrorType.network, 'CONNECTION_FAILED', '连接失败: $errorDetail', e);
      rethrow;
    }
  }

  @override
  Future<void> stopListening() async {
    if (!_isListening) return;

    try {
      _isListening = false;
      
      // 发送结束标志
      if (_channel != null) {
        final endFrame = _createEndFrame();
        _channel!.sink.add(endFrame);
        debugPrint('发送结束帧');
      }
      
      _updateConnectionState(SttConnectionState.connected);
      debugPrint('科大讯飞STT服务停止监听');
      
    } catch (e) {
      debugPrint('停止STT监听失败: $e');
      _emitError(SttErrorType.unknown, 'STOP_FAILED', '停止监听失败: $e', e);
    }
  }

  @override
  void sendAudioData(Uint8List audioData) {
    if (!_isListening || _channel == null) {
      debugPrint('STT服务未在监听状态，忽略音频数据');
      return;
    }

    try {
      // 创建音频数据帧
      final audioFrame = _createAudioFrame(audioData);
      _channel!.sink.add(audioFrame);
      
    } catch (e) {
      debugPrint('发送音频数据失败: $e');
      _emitError(SttErrorType.network, 'SEND_AUDIO_FAILED', '发送音频数据失败: $e', e);
    }
  }

  @override
  Future<void> reset() async {
    await disconnect();
    _totalCharacters = 0;
    _totalConfidence = 0.0;
    _resultCount = 0;
    _errorCount = 0;
    _sessionStartTime = null;
    _updateStats();
    debugPrint('科大讯飞STT服务已重置');
  }

  @override
  Future<void> disconnect() async {
    try {
      _isListening = false;
      
      if (_channel != null) {
        await _channel!.sink.close(status.normalClosure);
        _channel = null;
      }
      
      _updateConnectionState(SttConnectionState.disconnected);
      debugPrint('科大讯飞STT服务已断开连接');
      
    } catch (e) {
      debugPrint('断开STT连接失败: $e');
    }
  }

  @override
  Future<List<String>> getSupportedLanguages() async {
    // 科大讯飞支持的语言
    return ['zh-cn', 'en-us'];
  }

  @override
  Future<int> testConnection() async {
    try {
      final startTime = DateTime.now();
      final wsUrl = _generateAuthUrl();
      final testChannel = WebSocketChannel.connect(Uri.parse(wsUrl));
      
      await testChannel.sink.close();
      final endTime = DateTime.now();
      
      return endTime.difference(startTime).inMilliseconds;
    } catch (e) {
      debugPrint('连接测试失败: $e');
      return -1;
    }
  }

  /// 生成认证URL
  String _generateAuthUrl() {
    final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    
    // 构建签名字符串
    final signString = '$_appId$timestamp';
    final signature = _generateSignature(signString);
    
    // 构建URL参数
    final params = {
      'appid': _appId,
      'ts': timestamp.toString(),
      'signa': signature,
      'lang': _config.language,
      'pd': 'iat', // 语音识别
      'ptt': '0', // 实时
      'channel': '1', // 单声道
      'aus': '1', // 音频编码，1表示pcm
      'aue': 'raw', // 音频格式
      'rate': _config.sampleRate.toString(),
    };
    
    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return '$_baseUrl?$queryString';
  }

  /// 生成签名
  String _generateSignature(String signString) {
    final key = utf8.encode(_apiKey);
    final bytes = utf8.encode(signString);
    final hmac = Hmac(md5, key);
    final digest = hmac.convert(bytes);
    return base64Encode(digest.bytes);
  }

  /// 创建音频数据帧
  String _createAudioFrame(Uint8List audioData) {
    final frame = {
      'common': {
        'app_id': _appId,
      },
      'business': {
        'language': _config.language,
        'domain': 'iat',
        'accent': 'mandarin',
        'vinfo': 1,
        'vad_eos': _config.silenceTimeout,
      },
      'data': {
        'status': 1, // 1表示数据传输中
        'format': 'audio/L16;rate=${_config.sampleRate}',
        'audio': base64Encode(audioData),
        'encoding': 'raw',
      },
    };
    
    return jsonEncode(frame);
  }

  /// 创建结束帧
  String _createEndFrame() {
    final frame = {
      'data': {
        'status': 2, // 2表示数据传输结束
      },
    };
    
    return jsonEncode(frame);
  }

  /// 处理WebSocket消息
  void _onMessage(dynamic message) {
    try {
      final data = jsonDecode(message as String);
      _handleResponse(data);
    } catch (e) {
      debugPrint('解析STT响应失败: $e');
      _emitError(SttErrorType.server, 'PARSE_FAILED', '解析响应失败: $e', e);
    }
  }

  /// 处理响应数据
  void _handleResponse(Map<String, dynamic> data) {
    // 检查错误
    if (data['code'] != null && data['code'] != 0) {
      final errorCode = data['code'].toString();
      final errorMsg = data['message'] ?? '未知错误';

      debugPrint('❌ 科大讯飞STT服务器错误: 错误码 $errorCode - $errorMsg');

      // 针对10110错误的特殊处理
      if (errorCode == '10110') {
        debugPrint('网络连接不稳定，尝试重新连接...');
        // 不立即发送错误，而是尝试重连
        _attemptReconnect();
        return;
      }

      _emitError(SttErrorType.server, errorCode, '错误码：$errorCode | $errorMsg');
      return;
    }

    // 处理识别结果
    if (data['data'] != null) {
      final resultData = data['data'];
      if (resultData['result'] != null) {
        _processResult(resultData['result']);
      }
    }
  }

  /// 处理识别结果
  void _processResult(Map<String, dynamic> resultData) {
    try {
      final ws = resultData['ws'] as List?;
      if (ws == null || ws.isEmpty) return;

      final buffer = StringBuffer();
      double totalConfidence = 0.0;
      int wordCount = 0;

      for (final word in ws) {
        final cw = word['cw'] as List?;
        if (cw != null) {
          for (final char in cw) {
            final w = char['w'] as String?;
            final sc = char['sc'] as num?;
            
            if (w != null) {
              buffer.write(w);
              if (sc != null) {
                totalConfidence += sc.toDouble();
                wordCount++;
              }
            }
          }
        }
      }

      if (buffer.isNotEmpty) {
        final text = buffer.toString();
        final confidence = wordCount > 0 ? totalConfidence / wordCount : 0.0;
        final isFinal = resultData['ls'] == true;

        final result = SttResult(
          text: text,
          isFinal: isFinal,
          confidence: confidence,
          timestamp: DateTime.now(),
          segmentId: resultData['seg_id']?.toString(),
        );

        // 检查StreamController是否已关闭
        if (!_resultController.isClosed) {
          _resultController.add(result);
        }
        
        // 更新统计信息
        if (isFinal) {
          _totalCharacters += text.length;
          _totalConfidence += confidence;
          _resultCount++;
          _updateStats();
        }

        debugPrint('STT结果: ${result.text} (置信度: ${(confidence * 100).toStringAsFixed(1)}%, 最终: $isFinal)');
      }
    } catch (e) {
      debugPrint('处理STT结果失败: $e');
      _emitError(SttErrorType.server, 'PROCESS_RESULT_FAILED', '处理结果失败: $e', e);
    }
  }

  /// WebSocket错误处理
  void _onWebSocketError(error) {
    debugPrint('WebSocket错误: $error');
    _updateConnectionState(SttConnectionState.error);

    // 根据错误类型提供更详细的错误信息
    String errorMessage = 'WebSocket连接错误';
    String errorCode = 'WEBSOCKET_ERROR';

    if (error.toString().contains('10110')) {
      errorMessage = '网络连接超时，请检查网络连接';
      errorCode = 'NETWORK_TIMEOUT';
    } else if (error.toString().contains('10114')) {
      errorMessage = 'API认证失败，请检查密钥配置';
      errorCode = 'AUTH_FAILED';
    } else if (error.toString().contains('Connection')) {
      errorMessage = '无法连接到服务器，请检查网络';
      errorCode = 'CONNECTION_FAILED';
    }

    _emitError(SttErrorType.network, errorCode, errorMessage, error);
  }

  /// WebSocket连接关闭
  void _onWebSocketClosed() {
    debugPrint('WebSocket连接已关闭');
    _updateConnectionState(SttConnectionState.disconnected);
    _isListening = false;
  }

  /// 尝试重新连接
  Future<void> _attemptReconnect() async {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint('❌ 重连次数已达上限，停止重连');
      if (!_errorController.isClosed) {
        _emitError(SttErrorType.network, 'MAX_RECONNECT_EXCEEDED', '网络连接不稳定，已达最大重连次数');
      }
      return;
    }

    _reconnectAttempts++;
    debugPrint('🔄 尝试第 $_reconnectAttempts 次重连...');

    try {
      // 先关闭当前连接
      await stopListening();

      // 等待一段时间后重连
      await Future.delayed(Duration(seconds: _reconnectAttempts));

      // 检查是否已经被销毁
      if (_stateController.isClosed) {
        debugPrint('服务已被销毁，停止重连');
        return;
      }

      // 重新启动监听
      await startListening();

      debugPrint('✅ 重连成功！');
      _reconnectAttempts = 0; // 重置重连计数

    } catch (e) {
      debugPrint('❌ 第 $_reconnectAttempts 次重连失败: $e');

      // 检查是否已经被销毁
      if (_stateController.isClosed) {
        debugPrint('服务已被销毁，停止重连');
        return;
      }

      if (_reconnectAttempts < _maxReconnectAttempts) {
        // 继续尝试重连
        await _attemptReconnect();
      } else {
        if (!_errorController.isClosed) {
          _emitError(SttErrorType.network, 'RECONNECT_FAILED', '重连失败，请检查网络连接');
        }
      }
    }
  }

  /// 更新连接状态
  void _updateConnectionState(SttConnectionState newState) {
    if (_connectionState != newState) {
      _connectionState = newState;
      // 检查StreamController是否已关闭
      if (!_stateController.isClosed) {
        _stateController.add(newState);
      }
      notifyListeners();
    }
  }

  /// 发送错误事件
  void _emitError(SttErrorType type, String code, String message, [Object? originalError]) {
    _errorCount++;
    _updateStats();
    
    final error = SttError(
      code: code,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      originalError: originalError,
    );
    
    // 检查StreamController是否已关闭
    if (!_errorController.isClosed) {
      _errorController.add(error);
    }
  }

  /// 更新统计信息
  void _updateStats() {
    final duration = _sessionStartTime != null 
        ? DateTime.now().difference(_sessionStartTime!).inMilliseconds 
        : 0;
    
    final avgConfidence = _resultCount > 0 ? _totalConfidence / _resultCount : 0.0;
    
    _stats = SttStats(
      totalDuration: duration,
      totalCharacters: _totalCharacters,
      averageConfidence: avgConfidence,
      networkLatency: 0, // TODO: 实现网络延迟测量
      errorCount: _errorCount,
    );
  }

  @override
  void dispose() {
    _resultController.close();
    _errorController.close();
    _stateController.close();
    _endOfSpeechController.close();
    super.dispose();
  }
}
