import 'dart:async';
import 'package:flutter/foundation.dart';
import 'stt_models.dart';
import 'xunfei_stt_service.dart';

/// 流式语音转文字服务抽象接口
abstract class StreamingSttService extends ChangeNotifier {
  /// 当前连接状态
  SttConnectionState get connectionState;

  /// 服务配置
  SttConfig get config;

  /// 统计信息
  SttStats get stats;

  /// 是否正在监听
  bool get isListening;

  /// 识别结果流
  Stream<SttResult> get onResult;

  /// 错误事件流
  Stream<SttError> get onError;

  /// 连接状态变化流
  Stream<SttConnectionState> get onConnectionStateChanged;

  /// 语音结束事件流（检测到静音）
  Stream<void> get onEndOfSpeech;

  /// 初始化服务
  ///
  /// [config] STT配置参数
  /// 返回是否初始化成功
  Future<bool> initialize(SttConfig config);

  /// 开始监听语音
  ///
  /// 建立连接并开始接收音频数据
  /// 抛出 [SttError] 如果启动失败
  Future<void> startListening();

  /// 停止监听语音
  ///
  /// 停止接收音频数据但保持连接
  Future<void> stopListening();

  /// 发送音频数据
  ///
  /// [audioData] PCM音频数据
  /// 抛出 [SttError] 如果发送失败
  void sendAudioData(Uint8List audioData);

  /// 重置服务状态
  ///
  /// 清除所有缓存和状态，准备新的识别会话
  Future<void> reset();

  /// 断开连接
  ///
  /// 关闭所有连接和资源
  Future<void> disconnect();

  /// 获取支持的语言列表
  ///
  /// 返回支持的语言代码列表
  Future<List<String>> getSupportedLanguages();

  /// 测试连接
  ///
  /// 测试服务是否可用
  /// 返回连接延迟（毫秒），-1表示连接失败
  Future<int> testConnection();

  @override
  void dispose() {
    disconnect();
    super.dispose();
  }
}

/// STT服务工厂
class SttServiceFactory {
  /// 创建科大讯飞STT服务
  static StreamingSttService createXunfeiService({
    required String appId,
    required String apiKey,
    required String apiSecret,
  }) {
    // 延迟导入，避免循环依赖
    return XunfeiSttService(appId: appId, apiKey: apiKey, apiSecret: apiSecret);
  }

  /// 创建百度STT服务
  static StreamingSttService createBaiduService() {
    throw UnimplementedError('BaiduSttService not implemented yet');
  }

  /// 创建腾讯云STT服务
  static StreamingSttService createTencentService() {
    throw UnimplementedError('TencentSttService not implemented yet');
  }

  /// 根据配置创建默认STT服务
  static StreamingSttService? createDefault() {
    // 需要从配置服务获取密钥信息
    throw UnimplementedError(
        'Use SttConfigService.createDefaultService() instead');
  }
}

/// STT服务管理器
///
/// 提供统一的STT服务管理接口，支持服务切换和配置管理
class SttServiceManager extends ChangeNotifier {
  StreamingSttService? _currentService;
  SttConfig _config = const SttConfig();
  final List<SttResult> _resultHistory = [];
  final List<SttError> _errorHistory = [];

  /// 当前使用的STT服务
  StreamingSttService? get currentService => _currentService;

  /// 当前配置
  SttConfig get config => _config;

  /// 识别结果历史
  List<SttResult> get resultHistory => List.unmodifiable(_resultHistory);

  /// 错误历史
  List<SttError> get errorHistory => List.unmodifiable(_errorHistory);

  /// 是否有活跃的服务
  bool get hasActiveService => _currentService != null;

  /// 当前连接状态
  SttConnectionState get connectionState =>
      _currentService?.connectionState ?? SttConnectionState.disconnected;

  /// 设置STT服务
  Future<void> setService(StreamingSttService service,
      {SttConfig? config}) async {
    // 清理旧服务
    if (_currentService != null) {
      await _currentService!.disconnect();
      _currentService!.removeListener(_onServiceStateChanged);
    }

    _currentService = service;
    if (config != null) {
      _config = config;
    }

    // 监听新服务状态变化
    _currentService!.addListener(_onServiceStateChanged);

    // 监听识别结果
    _currentService!.onResult.listen(_onResult);
    _currentService!.onError.listen(_onError);

    // 初始化服务
    await _currentService!.initialize(_config);

    notifyListeners();
  }

  /// 更新配置
  Future<void> updateConfig(SttConfig newConfig) async {
    _config = newConfig;
    if (_currentService != null) {
      await _currentService!.reset();
      await _currentService!.initialize(_config);
    }
    notifyListeners();
  }

  /// 开始识别
  Future<void> startRecognition() async {
    if (_currentService == null) {
      throw StateError('No STT service available');
    }
    await _currentService!.startListening();
  }

  /// 停止识别
  Future<void> stopRecognition() async {
    if (_currentService == null) return;
    await _currentService!.stopListening();
  }

  /// 发送音频数据
  void sendAudio(Uint8List audioData) {
    _currentService?.sendAudioData(audioData);
  }

  /// 清除历史记录
  void clearHistory() {
    _resultHistory.clear();
    _errorHistory.clear();
    notifyListeners();
  }

  void _onServiceStateChanged() {
    notifyListeners();
  }

  void _onResult(SttResult result) {
    _resultHistory.add(result);
    // 只保留最近100条结果
    if (_resultHistory.length > 100) {
      _resultHistory.removeAt(0);
    }
    notifyListeners();
  }

  void _onError(SttError error) {
    _errorHistory.add(error);
    // 只保留最近50条错误
    if (_errorHistory.length > 50) {
      _errorHistory.removeAt(0);
    }
    notifyListeners();
  }

  @override
  void dispose() {
    _currentService?.dispose();
    super.dispose();
  }
}
