import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';

class AmbientSoundService extends ChangeNotifier {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isDayTime = true;
  bool _isPlaying = false;

  /// 获取播放状态
  bool get isPlaying => _isPlaying;

  AmbientSoundService() {
    _init();
  }

  Future<void> _init() async {
    // Configure the audio session for ambient sound.
    // This allows it to mix with other audio sources.
    // final session = await AudioSession.instance;
    // await session.configure(const AudioSessionConfiguration.ambient());

    await _audioPlayer.setLoopMode(LoopMode.one);
  }

  Future<void> setTime(bool isDay) async {
    if (isDay == _isDayTime && _isPlaying) return;

    _isDayTime = isDay;
    // Use a WAV file for night to test compatibility
    final soundAsset = _isDayTime
        ? 'assets/audio/day_ambient.mp3'
        : 'assets/audio/night_ambient.wav';

    try {
      // Set the asset and start playing.
      await _audioPlayer.setAsset(soundAsset);
      await _audioPlayer.play();
      _isPlaying = true;
    } catch (e) {
      // Handle asset not found or other errors.
      debugPrint("Could not play sound '$soundAsset': $e");
      _isPlaying = false;
    }
    notifyListeners();
  }

  // Lower the volume for recording
  Future<void> duck() async {
    await _audioPlayer.setVolume(0.2);
  }

  // Restore the volume
  Future<void> unduck() async {
    await _audioPlayer.setVolume(1.0);
  }

  // Pause the ambient sound temporarily
  Future<void> pause() async {
    if (_isPlaying) {
      await _audioPlayer.pause();
      notifyListeners();
    }
  }

  // Resume the ambient sound
  Future<void> resume() async {
    if (!_isPlaying) return;
    try {
      await _audioPlayer.play();
      notifyListeners();
    } catch (e) {
      debugPrint("Could not resume ambient sound: $e");
    }
  }

  // Stop the ambient sound completely
  Future<void> stop() async {
    await _audioPlayer.stop();
    _isPlaying = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
