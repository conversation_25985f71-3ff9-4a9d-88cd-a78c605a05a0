import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/stt/stt_models.dart';
import 'package:echo_cave/services/stt/stt_config_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('STT Service Tests', () {
    test('should create default STT config', () {
      final config = SttConfigService.createDefaultConfig();
      
      expect(config.sampleRate, equals(16000));
      expect(config.audioFormat, equals(SttAudioFormat.pcm16));
      expect(config.language, equals('zh-cn'));
      expect(config.enableInterimResults, isTrue);
      expect(config.enablePunctuation, isTrue);
    });

    test('should create SttResult correctly', () {
      final result = SttResult(
        text: '你好世界',
        isFinal: true,
        confidence: 0.95,
        timestamp: DateTime.now(),
        segmentId: 'seg_001',
      );

      expect(result.text, equals('你好世界'));
      expect(result.isFinal, isTrue);
      expect(result.confidence, equals(0.95));
      expect(result.segmentId, equals('seg_001'));
    });

    test('should create SttError correctly', () {
      final error = SttError(
        code: 'TEST_ERROR',
        message: '测试错误',
        type: SttErrorType.network,
        timestamp: DateTime.now(),
      );

      expect(error.code, equals('TEST_ERROR'));
      expect(error.message, equals('测试错误'));
      expect(error.type, equals(SttErrorType.network));
    });

    test('should validate config summary format', () {
      final summary = SttConfigService.getConfigSummary();
      
      expect(summary, isA<Map<String, String>>());
      expect(summary.containsKey('科大讯飞App ID'), isTrue);
      expect(summary.containsKey('科大讯飞API Key'), isTrue);
      expect(summary.containsKey('配置状态'), isTrue);
    });

    test('should validate configuration', () {
      final validation = SttConfigService.validateConfig();
      
      expect(validation, isA<Map<String, dynamic>>());
      expect(validation.containsKey('isValid'), isTrue);
      expect(validation.containsKey('errors'), isTrue);
      expect(validation.containsKey('warnings'), isTrue);
    });

    test('should handle SttStats correctly', () {
      final stats = SttStats(
        totalDuration: 5000,
        totalCharacters: 100,
        averageConfidence: 0.85,
        networkLatency: 200,
        errorCount: 2,
      );

      expect(stats.totalDuration, equals(5000));
      expect(stats.totalCharacters, equals(100));
      expect(stats.averageConfidence, equals(0.85));
      expect(stats.networkLatency, equals(200));
      expect(stats.errorCount, equals(2));
      
      final statsString = stats.toString();
      expect(statsString, contains('5000ms'));
      expect(statsString, contains('100'));
      expect(statsString, contains('85.0%'));
    });

    test('should copy SttConfig correctly', () {
      final originalConfig = SttConfigService.createDefaultConfig();
      final newConfig = originalConfig.copyWith(
        sampleRate: 8000,
        language: 'en-us',
      );

      expect(newConfig.sampleRate, equals(8000));
      expect(newConfig.language, equals('en-us'));
      expect(newConfig.audioFormat, equals(originalConfig.audioFormat));
      expect(newConfig.enableInterimResults, equals(originalConfig.enableInterimResults));
    });
  });
}
