import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 沉浸式粒子效果
class ParticleEffect extends StatefulWidget {
  final bool isActive;
  final Color color;
  final int particleCount;
  final double size;

  const ParticleEffect({
    super.key,
    required this.isActive,
    this.color = Colors.white,
    this.particleCount = 50,
    this.size = 300.0,
  });

  @override
  State<ParticleEffect> createState() => _ParticleEffectState();
}

class _ParticleEffectState extends State<ParticleEffect>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    
    _initializeParticles();
    
    if (widget.isActive) {
      _controller.repeat();
    }
  }

  void _initializeParticles() {
    _particles = List.generate(widget.particleCount, (index) {
      return Particle(
        x: Random().nextDouble() * widget.size,
        y: Random().nextDouble() * widget.size,
        vx: (Random().nextDouble() - 0.5) * 2,
        vy: (Random().nextDouble() - 0.5) * 2,
        size: Random().nextDouble() * 3 + 1,
        opacity: Random().nextDouble() * 0.5 + 0.2,
      );
    });
  }

  @override
  void didUpdateWidget(ParticleEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          _updateParticles();
          return CustomPaint(
            painter: ParticlePainter(
              particles: _particles,
              color: widget.color,
            ),
            size: Size(widget.size, widget.size),
          );
        },
      ),
    );
  }

  void _updateParticles() {
    for (final particle in _particles) {
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      // 边界检测
      if (particle.x < 0 || particle.x > widget.size) {
        particle.vx *= -1;
      }
      if (particle.y < 0 || particle.y > widget.size) {
        particle.vy *= -1;
      }
      
      // 保持在边界内
      particle.x = particle.x.clamp(0, widget.size);
      particle.y = particle.y.clamp(0, widget.size);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

/// 粒子数据类
class Particle {
  double x;
  double y;
  double vx;
  double vy;
  final double size;
  final double opacity;

  Particle({
    required this.x,
    required this.y,
    required this.vx,
    required this.vy,
    required this.size,
    required this.opacity,
  });
}

/// 粒子绘制器
class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final Color color;

  ParticlePainter({
    required this.particles,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    for (final particle in particles) {
      paint.color = color.withValues(alpha: particle.opacity);
      canvas.drawCircle(
        Offset(particle.x, particle.y),
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(ParticlePainter oldDelegate) {
    return true;
  }
}

/// 呼吸光效
class BreathingGlow extends StatefulWidget {
  final Widget child;
  final Color glowColor;
  final double intensity;
  final bool isActive;

  const BreathingGlow({
    super.key,
    required this.child,
    this.glowColor = Colors.tealAccent,
    this.intensity = 1.0,
    this.isActive = true,
  });

  @override
  State<BreathingGlow> createState() => _BreathingGlowState();
}

class _BreathingGlowState extends State<BreathingGlow>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    if (widget.isActive) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(BreathingGlow oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            boxShadow: widget.isActive
                ? [
                    BoxShadow(
                      color: widget.glowColor.withValues(
                        alpha: _animation.value * widget.intensity * 0.5,
                      ),
                      blurRadius: 30 * _animation.value * widget.intensity,
                      spreadRadius: 10 * _animation.value * widget.intensity,
                    ),
                  ]
                : null,
          ),
          child: widget.child,
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

/// 触觉反馈工具类
class HapticUtils {
  /// 轻微触觉反馈
  static void light() {
    HapticFeedback.lightImpact();
  }

  /// 中等触觉反馈
  static void medium() {
    HapticFeedback.mediumImpact();
  }

  /// 强烈触觉反馈
  static void heavy() {
    HapticFeedback.heavyImpact();
  }

  /// 选择触觉反馈
  static void selection() {
    HapticFeedback.selectionClick();
  }

  /// 情感触觉反馈
  static void emotion(double intensity) {
    if (intensity > 0.8) {
      heavy();
    } else if (intensity > 0.5) {
      medium();
    } else {
      light();
    }
  }
}

/// 渐变文字效果
class GradientText extends StatelessWidget {
  final String text;
  final TextStyle style;
  final Gradient gradient;

  const GradientText({
    super.key,
    required this.text,
    required this.style,
    required this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Text(text, style: style),
    );
  }
}

/// 脉冲动画容器
class PulseContainer extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final double minScale;
  final double maxScale;
  final bool isActive;

  const PulseContainer({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 1000),
    this.minScale = 0.95,
    this.maxScale = 1.05,
    this.isActive = true,
  });

  @override
  State<PulseContainer> createState() => _PulseContainerState();
}

class _PulseContainerState extends State<PulseContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(
      begin: widget.minScale,
      end: widget.maxScale,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    
    if (widget.isActive) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(PulseContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: widget.child,
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
