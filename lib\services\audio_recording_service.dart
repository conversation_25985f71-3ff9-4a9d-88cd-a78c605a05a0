import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class AudioRecordingServiceNotifier extends ChangeNotifier {
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  bool _isRecorderInitialized = false;
  bool _isPlayerInitialized = false;
  String? _currentRecordingPath;
  bool _isRecording = false;
  bool _isPlaying = false;

  Stream<RecordingDisposition>? get onProgress => _recorder?.onProgress;

  AudioRecordingServiceNotifier() {
    initialize();
  }

  // Initialize the recorder and player
  Future<void> initialize() async {
    if (_isRecorderInitialized) {
      return;
    }

    try {
      _recorder = FlutterSoundRecorder();

      // Check microphone permission
      final status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        throw Exception('Microphone permission not granted');
      }

      await _recorder!.openRecorder();
      _isRecorderInitialized = true;
      notifyListeners();

      // Player initialization
      _player = FlutterSoundPlayer();
      await _player!.openPlayer();
      _isPlayerInitialized = true;
      notifyListeners();
    } catch (e) {
      print('Failed to initialize recorder: $e');
    }
  }

  // Start recording
  Future<void> startRecording() async {
    if (!_isRecorderInitialized) {
      await initialize();
    }

    if (_isRecording) return;

    try {
      // Generate a unique filename for the recording
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentRecordingPath = '${directory.path}/recording_$timestamp.aac';

      await _recorder!.setSubscriptionDuration(
        const Duration(milliseconds: 100),
      );

      await _recorder!.startRecorder(
        toFile: _currentRecordingPath,
        codec: Codec.aacADTS, // Good balance of quality and file size
      );
      _isRecording = true;
      notifyListeners();
    } catch (e) {
      print('Failed to start recording: $e');
    }
  }

  // Stop recording and return the file path
  Future<String?> stopRecording() async {
    if (!_isRecorderInitialized || !_isRecording) {
      return null;
    }

    try {
      await _recorder!.stopRecorder();
      _isRecording = false;
      final recordingPath = _currentRecordingPath;
      _currentRecordingPath = null;
      notifyListeners();
      return recordingPath;
    } catch (e) {
      print('Failed to stop recording: $e');
      return null;
    }
  }

  // Start playing a file
  Future<void> play(String path) async {
    if (!_isPlayerInitialized) {
      await initialize();
    }
    if (_isPlaying) {
      await stopPlayer();
    }

    await _player!.startPlayer(
      fromURI: path,
      whenFinished: () {
        _isPlaying = false;
        notifyListeners();
      },
    );
    _isPlaying = true;
    notifyListeners();
  }

  // Stop the player
  Future<void> stopPlayer() async {
    if (_isPlaying) {
      await _player!.stopPlayer();
      _isPlaying = false;
      notifyListeners();
    }
  }

  // Check if currently recording or playing
  bool get isRecording => _isRecording;
  bool get isPlaying => _isPlaying;

  // Check if recorder is initialized
  bool get isInitialized => _isRecorderInitialized;

  // Dispose of the recorder and player
  Future<void> dispose() async {
    if (_isRecorderInitialized) {
      await _recorder?.closeRecorder();
      _recorder = null;
      _isRecorderInitialized = false;
    }
    if (_isPlayerInitialized) {
      await _player?.closePlayer();
      _player = null;
      _isPlayerInitialized = false;
    }
  }
}
