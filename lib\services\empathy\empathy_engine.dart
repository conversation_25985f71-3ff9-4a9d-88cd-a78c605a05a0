import 'dart:async';
import 'package:flutter/foundation.dart';
import '../stt/stt_models.dart';
import '../stt/audio_stt_bridge.dart';
import '../tts_service.dart';
import 'emotion_analysis_service.dart';
import 'empathy_trigger_service.dart';
import 'empathy_ai_service.dart';
import 'emotion_models.dart';

/// 实时共鸣触发引擎
/// 
/// 整合情感分析、触发策略和AI回应生成的核心服务
class EmpathyEngine extends ChangeNotifier {
  // 服务依赖
  final AudioSttBridge _sttBridge;
  final EmotionAnalysisService _emotionService;
  final EmpathyTriggerService _triggerService;
  final EmpathyAiService _aiService;
  
  // 状态管理
  bool _isActive = false;
  bool _isInitialized = false;
  
  // 订阅管理
  StreamSubscription<SttResult>? _sttSubscription;
  StreamSubscription<EmotionAnalysis>? _emotionSubscription;
  StreamSubscription<EmpathyTrigger>? _triggerSubscription;
  
  // 事件流
  final StreamController<EmotionAnalysis> _emotionController = 
      StreamController<EmotionAnalysis>.broadcast();
  final StreamController<EmpathyTrigger> _triggerController = 
      StreamController<EmpathyTrigger>.broadcast();
  final StreamController<String> _responseController = 
      StreamController<String>.broadcast();
  
  // 统计信息
  int _totalAnalyses = 0;
  int _totalTriggers = 0;
  int _totalResponses = 0;
  DateTime? _sessionStartTime;

  EmpathyEngine({
    required AudioSttBridge sttBridge,
    required TTSService ttsService,
  }) : _sttBridge = sttBridge,
       _emotionService = EmotionAnalysisService(),
       _triggerService = EmpathyTriggerService(),
       _aiService = EmpathyAiService(ttsService: ttsService);

  /// 引擎状态
  bool get isActive => _isActive;
  bool get isInitialized => _isInitialized;
  
  /// 服务状态
  bool get isSttActive => _sttBridge.isActive;
  bool get isEmotionSessionActive => _emotionService.isSessionActive;
  bool get isTriggerSessionActive => _triggerService.isSessionActive;
  bool get isAiGenerating => _aiService.isGenerating;
  
  /// 事件流
  Stream<EmotionAnalysis> get onEmotionAnalysis => _emotionController.stream;
  Stream<EmpathyTrigger> get onTrigger => _triggerController.stream;
  Stream<String> get onResponse => _responseController.stream;
  
  /// 历史数据
  List<EmotionAnalysis> get emotionHistory => _emotionService.emotionHistory;
  List<EmpathyTrigger> get triggerHistory => _triggerService.triggerHistory;
  String? get lastResponse => _aiService.lastResponse;

  /// 初始化引擎
  Future<bool> initialize() async {
    try {
      // 确保STT桥接服务已初始化
      if (!_sttBridge.isInitialized) {
        final sttInitialized = await _sttBridge.initialize();
        if (!sttInitialized) {
          debugPrint('STT桥接服务初始化失败');
          return false;
        }
      }

      _isInitialized = true;
      notifyListeners();
      
      debugPrint('共鸣引擎初始化成功');
      return true;

    } catch (e) {
      debugPrint('共鸣引擎初始化失败: $e');
      return false;
    }
  }

  /// 启动共鸣引擎
  Future<void> start() async {
    if (!_isInitialized || _isActive) {
      debugPrint('共鸣引擎未初始化或已在运行中');
      return;
    }

    try {
      // 启动各个服务
      _emotionService.startSession();
      _triggerService.startSession();
      
      // 启动STT识别
      await _sttBridge.startRecognition();
      
      // 订阅STT结果
      _sttSubscription = _sttBridge.onResult.listen(_onSttResult);
      
      // 订阅情感分析结果
      _emotionSubscription = _emotionService.onEmotionAnalysis.listen(_onEmotionAnalysis);
      
      // 订阅触发事件
      _triggerSubscription = _triggerService.onTrigger.listen(_onTrigger);

      _isActive = true;
      _sessionStartTime = DateTime.now();
      _totalAnalyses = 0;
      _totalTriggers = 0;
      _totalResponses = 0;
      
      notifyListeners();
      debugPrint('共鸣引擎已启动');

    } catch (e) {
      debugPrint('启动共鸣引擎失败: $e');
      await stop();
      rethrow;
    }
  }

  /// 停止共鸣引擎
  Future<void> stop() async {
    if (!_isActive) return;

    try {
      // 取消订阅
      await _sttSubscription?.cancel();
      await _emotionSubscription?.cancel();
      await _triggerSubscription?.cancel();
      
      _sttSubscription = null;
      _emotionSubscription = null;
      _triggerSubscription = null;

      // 停止STT识别
      await _sttBridge.stopRecognition();
      
      // 结束各个服务会话
      _emotionService.endSession();
      _triggerService.endSession();

      _isActive = false;
      notifyListeners();
      
      debugPrint('共鸣引擎已停止');

    } catch (e) {
      debugPrint('停止共鸣引擎失败: $e');
    }
  }

  /// 重置引擎状态
  Future<void> reset() async {
    await stop();
    
    _emotionService.clearHistory();
    _triggerService.clearHistory();
    
    _totalAnalyses = 0;
    _totalTriggers = 0;
    _totalResponses = 0;
    _sessionStartTime = null;
    
    notifyListeners();
    debugPrint('共鸣引擎已重置');
  }

  /// 处理STT识别结果
  Future<void> _onSttResult(SttResult sttResult) async {
    if (!_isActive || !sttResult.isFinal) return;

    try {
      // 进行情感分析
      await _emotionService.analyzeEmotion(sttResult);
      _totalAnalyses++;

      debugPrint('处理STT结果: ${sttResult.text}');
      
    } catch (e) {
      debugPrint('处理STT结果失败: $e');
    }
  }

  /// 处理情感分析结果
  void _onEmotionAnalysis(EmotionAnalysis emotionAnalysis) {
    if (!_isActive) return;

    try {
      // 转发情感分析事件
      _emotionController.add(emotionAnalysis);
      
      // 获取最新的语音特征
      final speechFeatures = _emotionService.speechHistory.isNotEmpty
          ? _emotionService.speechHistory.last
          : null;
      
      // 触发共鸣分析
      _triggerService.analyzeAndTrigger(
        emotionAnalysis: emotionAnalysis,
        speechFeatures: speechFeatures,
      );
      
    } catch (e) {
      debugPrint('处理情感分析结果失败: $e');
    }
  }

  /// 处理触发事件
  Future<void> _onTrigger(EmpathyTrigger trigger) async {
    if (!_isActive) return;

    try {
      _totalTriggers++;
      
      // 转发触发事件
      _triggerController.add(trigger);
      
      // 检查是否应该生成AI回应
      if (_aiService.shouldRespond(trigger)) {
        final response = await _aiService.generateEmpathyResponse(trigger);
        
        if (response != null) {
          _totalResponses++;
          _responseController.add(response);
        }
      }
      
    } catch (e) {
      debugPrint('处理触发事件失败: $e');
    }
  }

  /// 手动触发共鸣回应
  Future<String?> manualTrigger({
    required String text,
    EmotionType? emotionType,
    EmpathyResponseType? responseType,
  }) async {
    if (!_isActive) return null;

    try {
      // 创建手动触发器
      final trigger = EmpathyTrigger(
        type: EmpathyTriggerType.emotionalKeyword,
        strength: 0.8,
        reason: '手动触发: $text',
        timestamp: DateTime.now(),
        suggestedResponse: responseType ?? EmpathyResponseType.understanding,
        emotionAnalysis: EmotionAnalysis(
          primaryEmotion: emotionType ?? EmotionType.neutral,
          intensity: EmotionIntensity.medium,
          confidence: 1.0,
          keywords: [text],
          emotionScore: 0.0,
          timestamp: DateTime.now(),
          originalText: text,
        ),
      );

      return await _aiService.generateEmpathyResponse(trigger);

    } catch (e) {
      debugPrint('手动触发失败: $e');
      return null;
    }
  }

  /// 获取引擎统计信息
  Map<String, dynamic> getEngineStats() {
    final sessionDuration = _sessionStartTime != null
        ? DateTime.now().difference(_sessionStartTime!).inMinutes
        : 0;

    return {
      'isActive': _isActive,
      'isInitialized': _isInitialized,
      'sessionDuration': sessionDuration,
      'totalAnalyses': _totalAnalyses,
      'totalTriggers': _totalTriggers,
      'totalResponses': _totalResponses,
      'analysisRate': sessionDuration > 0 ? _totalAnalyses / sessionDuration : 0.0,
      'triggerRate': sessionDuration > 0 ? _totalTriggers / sessionDuration : 0.0,
      'responseRate': _totalTriggers > 0 ? _totalResponses / _totalTriggers : 0.0,
      'sttStatus': _sttBridge.isActive ? '活跃' : '未活跃',
      'emotionServiceStatus': _emotionService.isSessionActive ? '活跃' : '未活跃',
      'triggerServiceStatus': _triggerService.isSessionActive ? '活跃' : '未活跃',
      'aiServiceStatus': _aiService.isGenerating ? '生成中' : '就绪',
    };
  }

  /// 获取当前情感状态摘要
  Map<String, dynamic> getCurrentEmotionSummary() {
    if (_emotionService.emotionHistory.isEmpty) {
      return {'status': '暂无数据'};
    }

    final recentEmotion = _emotionService.emotionHistory.last;
    final emotionTrend = _emotionService.getEmotionTrend();
    final averageScore = _emotionService.getAverageEmotionScore();

    return {
      'currentEmotion': recentEmotion.primaryEmotion.name,
      'emotionScore': recentEmotion.emotionScore,
      'intensity': recentEmotion.intensity.name,
      'confidence': recentEmotion.confidence,
      'trend': emotionTrend?.name ?? '未知',
      'averageScore': averageScore,
      'keywords': recentEmotion.keywords,
    };
  }

  @override
  void dispose() {
    stop();
    _emotionController.close();
    _triggerController.close();
    _responseController.close();
    _emotionService.dispose();
    _triggerService.dispose();
    _aiService.dispose();
    super.dispose();
  }
}
