import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/parallel_audio_io_service.dart';
import '../services/stt/audio_stt_bridge.dart';
import '../services/stt/stt_models.dart';
import '../services/stt/stt_config_service.dart';

/// STT语音识别测试界面
class SttTestView extends HookConsumerWidget {
  const SttTestView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 服务实例
    final audioService = useMemoized(() => ParallelAudioIOService());
    final sttBridge = useMemoized(() => AudioSttBridge(audioService));
    
    // 状态管理
    final isInitialized = useState(false);
    final isRecognizing = useState(false);
    final recognitionResults = useState<List<SttResult>>([]);
    final errorMessages = useState<List<SttError>>([]);
    final configSummary = useState<Map<String, String>>({});

    // 初始化
    useEffect(() {
      _initializeServices(sttBridge, isInitialized, configSummary);
      
      // 监听识别结果
      final resultSubscription = sttBridge.onResult.listen((result) {
        recognitionResults.value = [...recognitionResults.value, result];
      });
      
      // 监听错误
      final errorSubscription = sttBridge.onError.listen((error) {
        errorMessages.value = [...errorMessages.value, error];
      });

      return () {
        resultSubscription.cancel();
        errorSubscription.cancel();
        sttBridge.dispose();
      };
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      appBar: AppBar(
        title: const Text('STT语音识别测试'),
        backgroundColor: const Color(0xFF16213E),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 状态信息卡片
            _buildStatusCard(sttBridge, isInitialized.value, configSummary.value),
            
            const SizedBox(height: 16),
            
            // 控制按钮
            _buildControlButtons(
              sttBridge, 
              isInitialized.value, 
              isRecognizing, 
              recognitionResults,
              errorMessages,
            ),
            
            const SizedBox(height: 16),
            
            // 识别结果显示
            Expanded(
              child: _buildResultsDisplay(recognitionResults.value, errorMessages.value),
            ),
          ],
        ),
      ),
    );
  }

  /// 初始化服务
  Future<void> _initializeServices(
    AudioSttBridge sttBridge,
    ValueNotifier<bool> isInitialized,
    ValueNotifier<Map<String, String>> configSummary,
  ) async {
    try {
      final success = await sttBridge.initialize();
      isInitialized.value = success;
      configSummary.value = sttBridge.getConfigSummary();
    } catch (e) {
      debugPrint('初始化STT服务失败: $e');
    }
  }

  /// 构建状态信息卡片
  Widget _buildStatusCard(AudioSttBridge sttBridge, bool isInitialized, Map<String, String> configSummary) {
    return Card(
      color: const Color(0xFF16213E),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isInitialized ? Icons.check_circle : Icons.error,
                  color: isInitialized ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'STT服务状态',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 服务状态
            _buildStatusRow('初始化状态', isInitialized ? '已初始化' : '未初始化'),
            _buildStatusRow('STT连接', _getConnectionStateText(sttBridge.sttConnectionState)),
            _buildStatusRow('音频服务', sttBridge.hasAudioService ? '可用' : '不可用'),
            
            const SizedBox(height: 8),
            const Divider(color: Colors.white24),
            const SizedBox(height: 8),
            
            // 配置信息
            const Text(
              '配置信息',
              style: TextStyle(color: Colors.white70, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            
            ...configSummary.entries.map((entry) => 
              _buildStatusRow(entry.key, entry.value)
            ),
          ],
        ),
      ),
    );
  }

  /// 构建状态行
  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70),
          ),
          Text(
            value,
            style: const TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// 获取连接状态文本
  String _getConnectionStateText(SttConnectionState state) {
    switch (state) {
      case SttConnectionState.disconnected:
        return '未连接';
      case SttConnectionState.connecting:
        return '连接中';
      case SttConnectionState.connected:
        return '已连接';
      case SttConnectionState.listening:
        return '监听中';
      case SttConnectionState.error:
        return '错误';
    }
  }

  /// 构建控制按钮
  Widget _buildControlButtons(
    AudioSttBridge sttBridge,
    bool isInitialized,
    ValueNotifier<bool> isRecognizing,
    ValueNotifier<List<SttResult>> recognitionResults,
    ValueNotifier<List<SttError>> errorMessages,
  ) {
    return Row(
      children: [
        // 开始识别按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isInitialized && !isRecognizing.value
                ? () => _startRecognition(sttBridge, isRecognizing)
                : null,
            icon: const Icon(Icons.mic),
            label: const Text('开始识别'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 停止识别按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isRecognizing.value
                ? () => _stopRecognition(sttBridge, isRecognizing)
                : null,
            icon: const Icon(Icons.stop),
            label: const Text('停止识别'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 清除结果按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _clearResults(recognitionResults, errorMessages),
            icon: const Icon(Icons.clear),
            label: const Text('清除'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 开始识别
  Future<void> _startRecognition(AudioSttBridge sttBridge, ValueNotifier<bool> isRecognizing) async {
    try {
      await sttBridge.startRecognition();
      isRecognizing.value = true;
    } catch (e) {
      debugPrint('开始识别失败: $e');
    }
  }

  /// 停止识别
  Future<void> _stopRecognition(AudioSttBridge sttBridge, ValueNotifier<bool> isRecognizing) async {
    try {
      await sttBridge.stopRecognition();
      isRecognizing.value = false;
    } catch (e) {
      debugPrint('停止识别失败: $e');
    }
  }

  /// 清除结果
  void _clearResults(
    ValueNotifier<List<SttResult>> recognitionResults,
    ValueNotifier<List<SttError>> errorMessages,
  ) {
    recognitionResults.value = [];
    errorMessages.value = [];
  }

  /// 构建结果显示区域
  Widget _buildResultsDisplay(List<SttResult> results, List<SttError> errors) {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          const TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white54,
            indicatorColor: Colors.tealAccent,
            tabs: [
              Tab(text: '识别结果'),
              Tab(text: '错误日志'),
            ],
          ),
          
          Expanded(
            child: TabBarView(
              children: [
                // 识别结果页
                _buildResultsList(results),
                
                // 错误日志页
                _buildErrorsList(errors),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建识别结果列表
  Widget _buildResultsList(List<SttResult> results) {
    if (results.isEmpty) {
      return const Center(
        child: Text(
          '暂无识别结果\n点击"开始识别"按钮开始语音识别',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final result = results[index];
        return Card(
          color: const Color(0xFF1A1A2E),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Icon(
              result.isFinal ? Icons.check_circle : Icons.radio_button_unchecked,
              color: result.isFinal ? Colors.green : Colors.orange,
            ),
            title: Text(
              result.text,
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Text(
              '置信度: ${(result.confidence * 100).toStringAsFixed(1)}% | ${result.timestamp.toString().substring(11, 19)}',
              style: const TextStyle(color: Colors.white54, fontSize: 12),
            ),
            trailing: result.isFinal 
                ? const Icon(Icons.done, color: Colors.green, size: 16)
                : const Icon(Icons.more_horiz, color: Colors.orange, size: 16),
          ),
        );
      },
    );
  }

  /// 构建错误列表
  Widget _buildErrorsList(List<SttError> errors) {
    if (errors.isEmpty) {
      return const Center(
        child: Text(
          '暂无错误记录',
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return ListView.builder(
      itemCount: errors.length,
      itemBuilder: (context, index) {
        final error = errors[index];
        return Card(
          color: const Color(0xFF2D1B1B),
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: const Icon(Icons.error, color: Colors.red),
            title: Text(
              error.message,
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: Text(
              '错误码: ${error.code} | 类型: ${error.type.name} | ${error.timestamp.toString().substring(11, 19)}',
              style: const TextStyle(color: Colors.white54, fontSize: 12),
            ),
          ),
        );
      },
    );
  }
}
