{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Program Files/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-3e14d3a25b9b9110f599.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-d5996a608e4ed4bd7c94.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6da7a145f0023347ff22.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-d5996a608e4ed4bd7c94.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-6da7a145f0023347ff22.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-3e14d3a25b9b9110f599.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}