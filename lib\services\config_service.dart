import 'package:flutter_dotenv/flutter_dotenv.dart';

/// 配置服务，统一管理应用的环境变量配置
class ConfigService {
  // 单例模式
  ConfigService._internal();
  static final ConfigService _instance = ConfigService._internal();
  static ConfigService get instance => _instance;

  // AI服务配置
  String get deepseekApiKey => dotenv.env['DEEPSEEK_API_KEY'] ?? '';
  String get deepseekBaseUrl =>
      dotenv.env['DEEPSEEK_BASE_URL'] ?? 'https://api.deepseek.com';

  // 应用配置
  String get appName => dotenv.env['APP_NAME'] ?? 'Echo Cave';
  String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';

  // 调试模式
  bool get debugMode {
    final debug = dotenv.env['DEBUG_MODE']?.toLowerCase();
    return debug == 'true' || debug == '1';
  }

  // TTS配置
  String get defaultTtsLanguage =>
      dotenv.env['DEFAULT_TTS_LANGUAGE'] ?? 'zh-CN';
  double get defaultTtsRate {
    final rateStr = dotenv.env['DEFAULT_TTS_RATE'];
    return double.tryParse(rateStr ?? '0.4') ?? 0.4;
  }

  double get defaultTtsPitch {
    final pitchStr = dotenv.env['DEFAULT_TTS_PITCH'];
    return double.tryParse(pitchStr ?? '1.2') ?? 1.2;
  }

  /// 检查必要的配置是否存在
  bool get isConfigured => deepseekApiKey.isNotEmpty;

  /// 打印配置信息（仅在调试模式下显示敏感信息的部分内容）
  void printConfig() {
    if (debugMode) {
      print('=== Echo Cave 配置信息 ===');
      print('应用名称: $appName');
      print('应用版本: $appVersion');
      print('调试模式: $debugMode');
      print('DeepSeek Base URL: $deepseekBaseUrl');
      print('DeepSeek API Key: ${_maskApiKey(deepseekApiKey)}');
      print('TTS语言: $defaultTtsLanguage');
      print('TTS语速: $defaultTtsRate');
      print('TTS音调: $defaultTtsPitch');
      print('========================');
    }
  }

  /// 遮蔽API Key的敏感部分
  String _maskApiKey(String apiKey) {
    if (apiKey.isEmpty) return '未设置';
    if (apiKey.length <= 8) return '***';
    return '${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}';
  }
}
