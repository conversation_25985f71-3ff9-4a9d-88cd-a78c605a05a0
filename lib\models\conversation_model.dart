import 'package:flutter/foundation.dart';

@immutable
class Message {
  final String sender; // 'USER' or 'AI'
  final String textContent;
  final DateTime timestamp;

  const Message({
    required this.sender,
    required this.textContent,
    required this.timestamp,
  });
}

@immutable
class Conversation {
  final String id;
  final List<Message> messages;

  const Conversation({
    required this.id,
    required this.messages,
  });
}
