import 'package:flutter/foundation.dart';

@immutable
class Message {
  final String id;
  final String content;
  final bool isFromUser;
  final DateTime timestamp;

  const Message({
    required this.id,
    required this.content,
    required this.isFromUser,
    required this.timestamp,
  });

  // 便利构造函数，用于从旧的sender字段迁移
  factory Message.fromSender({
    required String id,
    required String sender,
    required String textContent,
    required DateTime timestamp,
  }) {
    return Message(
      id: id,
      content: textContent,
      isFromUser: sender == 'USER',
      timestamp: timestamp,
    );
  }

  // 转换为JSON的方法
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'isFromUser': isFromUser,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  // 从JSON创建Message的工厂方法
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as String,
      content: json['content'] as String,
      isFromUser: json['isFromUser'] as bool,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
    );
  }

  // 获取发送者字符串（向后兼容）
  String get sender => isFromUser ? 'USER' : 'AI';

  // 获取文本内容（向后兼容）
  String get textContent => content;
}

@immutable
class Conversation {
  final String id;
  final List<Message> messages;

  const Conversation({
    required this.id,
    required this.messages,
  });
}
