import 'package:flutter/foundation.dart';

/// 情感类型
enum EmotionType {
  /// 积极情感
  positive,
  /// 消极情感
  negative,
  /// 中性情感
  neutral,
  /// 焦虑
  anxious,
  /// 悲伤
  sad,
  /// 愤怒
  angry,
  /// 快乐
  happy,
  /// 恐惧
  fearful,
  /// 困惑
  confused,
  /// 孤独
  lonely,
  /// 压力
  stressed,
  /// 希望
  hopeful,
}

/// 情感强度
enum EmotionIntensity {
  /// 轻微
  low,
  /// 中等
  medium,
  /// 强烈
  high,
  /// 极强
  extreme,
}

/// 情感分析结果
@immutable
class EmotionAnalysis {
  /// 主要情感类型
  final EmotionType primaryEmotion;
  
  /// 情感强度
  final EmotionIntensity intensity;
  
  /// 置信度 (0.0-1.0)
  final double confidence;
  
  /// 检测到的关键词
  final List<String> keywords;
  
  /// 情感分数 (-1.0 到 1.0，负数表示消极，正数表示积极)
  final double emotionScore;
  
  /// 分析时间戳
  final DateTime timestamp;
  
  /// 原始文本
  final String originalText;
  
  /// 次要情感（如果有）
  final List<EmotionType> secondaryEmotions;

  const EmotionAnalysis({
    required this.primaryEmotion,
    required this.intensity,
    required this.confidence,
    required this.keywords,
    required this.emotionScore,
    required this.timestamp,
    required this.originalText,
    this.secondaryEmotions = const [],
  });

  /// 是否为消极情感
  bool get isNegative => emotionScore < -0.1;
  
  /// 是否为积极情感
  bool get isPositive => emotionScore > 0.1;
  
  /// 是否为中性情感
  bool get isNeutral => emotionScore >= -0.1 && emotionScore <= 0.1;
  
  /// 是否需要共鸣回应
  bool get needsEmpathy {
    return isNegative && 
           (intensity == EmotionIntensity.medium || intensity == EmotionIntensity.high) &&
           confidence > 0.6;
  }

  @override
  String toString() {
    return 'EmotionAnalysis(emotion: $primaryEmotion, intensity: $intensity, score: ${emotionScore.toStringAsFixed(2)}, confidence: ${(confidence * 100).toStringAsFixed(1)}%)';
  }
}

/// 语音特征分析
@immutable
class SpeechFeatures {
  /// 语速 (字符/秒)
  final double speechRate;
  
  /// 停顿时长 (秒)
  final double pauseDuration;
  
  /// 语音连续性 (0.0-1.0)
  final double continuity;
  
  /// 是否检测到停顿
  final bool hasPause;
  
  /// 语音片段长度 (秒)
  final double segmentDuration;
  
  /// 分析时间戳
  final DateTime timestamp;

  const SpeechFeatures({
    required this.speechRate,
    required this.pauseDuration,
    required this.continuity,
    required this.hasPause,
    required this.segmentDuration,
    required this.timestamp,
  });

  /// 是否语速过快（可能表示焦虑）
  bool get isSpeakingFast => speechRate > 8.0;
  
  /// 是否语速过慢（可能表示悲伤）
  bool get isSpeakingSlow => speechRate < 3.0;
  
  /// 是否有长时间停顿（可能表示思考或情绪波动）
  bool get hasLongPause => pauseDuration > 2.0;
  
  /// 语音流畅度评分 (0.0-1.0)
  double get fluencyScore => continuity * (1.0 - (pauseDuration / 10.0).clamp(0.0, 1.0));

  @override
  String toString() {
    return 'SpeechFeatures(rate: ${speechRate.toStringAsFixed(1)} chars/s, pause: ${pauseDuration.toStringAsFixed(1)}s, continuity: ${(continuity * 100).toStringAsFixed(1)}%)';
  }
}

/// 共鸣触发条件
@immutable
class EmpathyTrigger {
  /// 触发类型
  final EmpathyTriggerType type;
  
  /// 触发强度 (0.0-1.0)
  final double strength;
  
  /// 触发原因
  final String reason;
  
  /// 相关的情感分析
  final EmotionAnalysis? emotionAnalysis;
  
  /// 相关的语音特征
  final SpeechFeatures? speechFeatures;
  
  /// 触发时间戳
  final DateTime timestamp;
  
  /// 建议的回应类型
  final EmpathyResponseType suggestedResponse;

  const EmpathyTrigger({
    required this.type,
    required this.strength,
    required this.reason,
    required this.timestamp,
    required this.suggestedResponse,
    this.emotionAnalysis,
    this.speechFeatures,
  });

  /// 是否应该立即触发回应
  bool get shouldTriggerImmediately => strength > 0.7;
  
  /// 是否应该延迟触发回应
  bool get shouldTriggerDelayed => strength > 0.4 && strength <= 0.7;

  @override
  String toString() {
    return 'EmpathyTrigger(type: $type, strength: ${(strength * 100).toStringAsFixed(1)}%, reason: $reason)';
  }
}

/// 共鸣触发类型
enum EmpathyTriggerType {
  /// 情感关键词触发
  emotionalKeyword,
  /// 语速异常触发
  speechRateAnomaly,
  /// 长时间停顿触发
  longPause,
  /// 情感强度触发
  emotionIntensity,
  /// 重复表达触发
  repetitiveExpression,
  /// 求助信号触发
  helpSignal,
  /// 情感转换触发
  emotionTransition,
}

/// 共鸣回应类型
enum EmpathyResponseType {
  /// 理解确认
  understanding,
  /// 情感支持
  emotionalSupport,
  /// 温和询问
  gentleInquiry,
  /// 陪伴安慰
  companionship,
  /// 积极鼓励
  encouragement,
  /// 静默陪伴
  silentPresence,
  /// 转移注意
  distraction,
}

/// 情感历史记录
@immutable
class EmotionHistory {
  /// 情感分析列表
  final List<EmotionAnalysis> analyses;
  
  /// 语音特征列表
  final List<SpeechFeatures> speechFeatures;
  
  /// 触发记录列表
  final List<EmpathyTrigger> triggers;
  
  /// 记录开始时间
  final DateTime startTime;
  
  /// 记录结束时间
  final DateTime? endTime;

  const EmotionHistory({
    required this.analyses,
    required this.speechFeatures,
    required this.triggers,
    required this.startTime,
    this.endTime,
  });

  /// 获取主要情感趋势
  EmotionType? get dominantEmotion {
    if (analyses.isEmpty) return null;
    
    final emotionCounts = <EmotionType, int>{};
    for (final analysis in analyses) {
      emotionCounts[analysis.primaryEmotion] = 
          (emotionCounts[analysis.primaryEmotion] ?? 0) + 1;
    }
    
    return emotionCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
  
  /// 获取平均情感分数
  double get averageEmotionScore {
    if (analyses.isEmpty) return 0.0;
    return analyses.map((a) => a.emotionScore).reduce((a, b) => a + b) / analyses.length;
  }
  
  /// 获取平均语速
  double get averageSpeechRate {
    if (speechFeatures.isEmpty) return 0.0;
    return speechFeatures.map((f) => f.speechRate).reduce((a, b) => a + b) / speechFeatures.length;
  }
  
  /// 获取触发频率 (次/分钟)
  double get triggerFrequency {
    if (triggers.isEmpty || endTime == null) return 0.0;
    final duration = endTime!.difference(startTime).inMinutes;
    return duration > 0 ? triggers.length / duration : 0.0;
  }

  /// 添加情感分析
  EmotionHistory addAnalysis(EmotionAnalysis analysis) {
    return EmotionHistory(
      analyses: [...analyses, analysis],
      speechFeatures: speechFeatures,
      triggers: triggers,
      startTime: startTime,
      endTime: endTime,
    );
  }
  
  /// 添加语音特征
  EmotionHistory addSpeechFeatures(SpeechFeatures features) {
    return EmotionHistory(
      analyses: analyses,
      speechFeatures: [...speechFeatures, features],
      triggers: triggers,
      startTime: startTime,
      endTime: endTime,
    );
  }
  
  /// 添加触发记录
  EmotionHistory addTrigger(EmpathyTrigger trigger) {
    return EmotionHistory(
      analyses: analyses,
      speechFeatures: speechFeatures,
      triggers: [...triggers, trigger],
      startTime: startTime,
      endTime: endTime,
    );
  }
}
