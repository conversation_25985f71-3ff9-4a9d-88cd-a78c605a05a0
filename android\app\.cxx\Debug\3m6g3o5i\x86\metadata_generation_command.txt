                        -HD:\flutter\sdk\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=D:\Program Files\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\Program Files\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\Program Files\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Program Files\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\ai-workspace\echo-cave\android\app\build\intermediates\cxx\Debug\3m6g3o5i\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\ai-workspace\echo-cave\android\app\build\intermediates\cxx\Debug\3m6g3o5i\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BD:\ai-workspace\echo-cave\android\app\.cxx\Debug\3m6g3o5i\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2