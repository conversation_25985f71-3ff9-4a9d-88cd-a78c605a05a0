import 'package:echo_cave/views/main_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:local_auth/local_auth.dart';
import 'package:echo_cave/main.dart'; // Import main.dart to access the key

class AuthGateView extends ConsumerStatefulWidget {
  const AuthGateView({super.key});

  @override
  ConsumerState<AuthGateView> createState() => _AuthGateViewState();
}

class _AuthGateViewState extends ConsumerState<AuthGateView> {
  final LocalAuthentication _localAuth = LocalAuthentication();

  @override
  void initState() {
    super.initState();
    _authenticate();
  }

  Future<void> _authenticate() async {
    bool authenticated = false;
    try {
      authenticated = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access Echo Cave',
        options: const AuthenticationOptions(
          stickyAuth: true, // Keep the dialog open on app resume
        ),
      );
    } on PlatformException catch (e) {
      // Handle exceptions like no biometrics available, etc.
      debugPrint('Error during authentication: $e');
      // In a real app, you'd show a proper error message.
      // For now, we'll just deny access.
      authenticated = false;
    }

    if (!mounted) return;

    if (authenticated) {
      // Use the global navigator key to ensure navigation works
      // regardless of the current context.
      navigatorKey.currentState?.pushReplacement(
        MaterialPageRoute(builder: (context) => const MainView()),
      );
    } else {
      // User did not authenticate, or an error occurred.
      // You could show a message or allow them to retry.
      // For this app's philosophy, maybe we just close the app.
      SystemNavigator.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
