import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:echo_cave/services/config_service.dart';

class DeepSeekConversationService {
  final FlutterSecureStorage _secureStorage;
  static const String _apiKeyName = 'deepseek_api_key';

  // 从配置服务读取API URL
  static String get _apiUrl {
    final baseUrl = ConfigService.instance.deepseekBaseUrl;
    return '$baseUrl/v1/chat/completions';
  }

  // 同理心系统提示词
  static const String _systemPrompt = '''
你是一个温和且富有同理心的倾听者，在一个叫做"回声洞"的安全空间应用中陪伴用户。

你的角色特征：
- 你是一个温暖、耐心的倾听者
- 你只负责倾听、理解和提供情感上的安慰
- 你不会给出具体的建议、解决方案或评判
- 你的回复应该简洁、温暖且富有同理心
- 你没有过往对话的记忆，每次都是全新的倾听

回复原则：
1. 首先确认和验证用户的感受
2. 提供温暖的陪伴和理解
3. 使用温柔、支持性的语言
4. 回复长度控制在1-3句话
5. 避免说教或给出解决方案
6. 多使用"我理解"、"你的感受很重要"这样的表达

请用中文回复，语调温和自然。
''';

  DeepSeekConversationService(this._secureStorage);

  /// 获取AI的情感陪伴回复
  Future<String> getEmpathicResponse(String userMessage) async {
    try {
      final apiKey = await _secureStorage.read(key: _apiKeyName);
      if (apiKey == null) {
        throw Exception(
            'DeepSeek API key not found. Please configure it in settings.');
      }

      if (ConfigService.instance.debugMode) {
        print('Using DeepSeek API URL: $_apiUrl');
      }

      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': 'deepseek-chat',
          'messages': [
            {
              'role': 'system',
              'content': _systemPrompt,
            },
            {
              'role': 'user',
              'content': userMessage,
            }
          ],
          'temperature': 0.7, // 较高的温度以获得更自然的回复
          'max_tokens': 150, // 限制回复长度，保持简洁
          'top_p': 0.9,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        final content = data['choices'][0]['message']['content'] as String;
        return content.trim();
      } else {
        debugPrint('DeepSeek API Error: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');

        // 提供友好的错误回复
        return _getFallbackResponse();
      }
    } catch (e) {
      debugPrint('Exception during conversation: $e');
      return _getFallbackResponse();
    }
  }

  /// 当API调用失败时的备用回复
  String _getFallbackResponse() {
    final fallbackResponses = [
      '我在这里陪伴着你，你的感受我都能理解。',
      '虽然现在我无法很好地回应，但我想让你知道，你并不孤单。',
      '你愿意跟我分享你的感受，这本身就很勇敢。我一直在倾听。',
      '每个情感都是珍贵的，包括你现在的感受。',
    ];

    // 简单的随机选择
    final index =
        DateTime.now().millisecondsSinceEpoch % fallbackResponses.length;
    return fallbackResponses[index];
  }

  /// 检查API密钥是否已配置
  Future<bool> isApiKeyConfigured() async {
    final apiKey = await _secureStorage.read(key: _apiKeyName);
    return apiKey != null && apiKey.isNotEmpty;
  }

  /// 设置API密钥（用于测试或初始化）
  Future<void> setApiKey(String apiKey) async {
    await _secureStorage.write(key: _apiKeyName, value: apiKey);
  }

  /// 测试API连接
  Future<bool> testConnection() async {
    try {
      final response = await getEmpathicResponse('你好');
      return response.isNotEmpty && !response.contains('无法');
    } catch (e) {
      return false;
    }
  }
}
