import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart';
import 'package:crypto/crypto.dart';

class EncryptionService {
  // Generate an IV (Initialization Vector) for AES encryption
  IV _generateIV() {
    return IV.fromSecureRandom(16); // 16 bytes for AES
  }

  // Create encrypter from base64 key string
  Encrypter _createEncrypter(String base64Key) {
    final keyBytes = base64.decode(base64Key);
    final key = Key(keyBytes);
    return Encrypter(AES(key));
  }

  // Encrypt file data
  EncryptedFileData encryptFileData(Uint8List fileData, String base64Key) {
    try {
      final encrypter = _createEncrypter(base64Key);
      final iv = _generateIV();

      // Handle empty data case
      if (fileData.isEmpty) {
        return EncryptedFileData(
          encryptedData: Uint8List(0),
          iv: iv.bytes,
          success: true,
        );
      }

      // Encrypt the file data
      final encrypted = encrypter.encryptBytes(fileData, iv: iv);

      return EncryptedFileData(
        encryptedData: encrypted.bytes,
        iv: iv.bytes,
        success: true,
      );
    } catch (e) {
      return EncryptedFileData(
        encryptedData: Uint8List(0),
        iv: Uint8List(0),
        success: false,
        errorMessage: 'Encryption failed: $e',
      );
    }
  }

  // Decrypt file data
  DecryptedFileData decryptFileData(
      Uint8List encryptedData, Uint8List ivBytes, String base64Key) {
    try {
      final encrypter = _createEncrypter(base64Key);
      final iv = IV(ivBytes);
      final encrypted = Encrypted(encryptedData);

      // Handle empty data case
      if (encryptedData.isEmpty) {
        return DecryptedFileData(
          decryptedData: Uint8List(0),
          success: true,
        );
      }

      // Decrypt the file data
      final decrypted = encrypter.decryptBytes(encrypted, iv: iv);

      return DecryptedFileData(
        decryptedData: Uint8List.fromList(decrypted),
        success: true,
      );
    } catch (e) {
      return DecryptedFileData(
        decryptedData: Uint8List(0),
        success: false,
        errorMessage: 'Decryption failed: $e',
      );
    }
  }

  // Encrypt a file and save to a new location
  Future<EncryptFileResult> encryptFile(
      String inputFilePath, String outputFilePath, String base64Key) async {
    try {
      // Read the input file
      final inputFile = File(inputFilePath);
      if (!await inputFile.exists()) {
        return EncryptFileResult(
          success: false,
          errorMessage: 'Input file does not exist: $inputFilePath',
        );
      }

      final fileData = await inputFile.readAsBytes();

      // Encrypt the data
      final encryptionResult = encryptFileData(fileData, base64Key);
      if (!encryptionResult.success) {
        return EncryptFileResult(
          success: false,
          errorMessage: encryptionResult.errorMessage,
        );
      }

      // Create encrypted file format: IV (16 bytes) + encrypted data
      final combinedData =
          Uint8List(16 + encryptionResult.encryptedData.length);
      combinedData.setRange(0, 16, encryptionResult.iv);
      combinedData.setRange(
          16, combinedData.length, encryptionResult.encryptedData);

      // Write to output file
      final outputFile = File(outputFilePath);
      await outputFile.writeAsBytes(combinedData);

      return EncryptFileResult(
        success: true,
        outputFilePath: outputFilePath,
        originalFileSize: fileData.length,
        encryptedFileSize: combinedData.length,
      );
    } catch (e) {
      return EncryptFileResult(
        success: false,
        errorMessage: 'File encryption failed: $e',
      );
    }
  }

  // Decrypt a file and save to a new location
  Future<DecryptFileResult> decryptFile(
      String inputFilePath, String outputFilePath, String base64Key) async {
    try {
      // Read the encrypted file
      final inputFile = File(inputFilePath);
      if (!await inputFile.exists()) {
        return DecryptFileResult(
          success: false,
          errorMessage: 'Input file does not exist: $inputFilePath',
        );
      }

      final encryptedFileData = await inputFile.readAsBytes();

      // Extract IV and encrypted data
      if (encryptedFileData.length < 16) {
        return DecryptFileResult(
          success: false,
          errorMessage: 'Invalid encrypted file format',
        );
      }

      final iv = encryptedFileData.sublist(0, 16);
      final encryptedData = encryptedFileData.sublist(16);

      // Decrypt the data
      final decryptionResult = decryptFileData(encryptedData, iv, base64Key);
      if (!decryptionResult.success) {
        return DecryptFileResult(
          success: false,
          errorMessage: decryptionResult.errorMessage,
        );
      }

      // Write to output file
      final outputFile = File(outputFilePath);
      await outputFile.writeAsBytes(decryptionResult.decryptedData);

      return DecryptFileResult(
        success: true,
        outputFilePath: outputFilePath,
        decryptedFileSize: decryptionResult.decryptedData.length,
      );
    } catch (e) {
      return DecryptFileResult(
        success: false,
        errorMessage: 'File decryption failed: $e',
      );
    }
  }

  // Generate a file hash for integrity verification
  String generateFileHash(Uint8List fileData) {
    return sha256.convert(fileData).toString();
  }
}

// Data classes for results
class EncryptedFileData {
  final Uint8List encryptedData;
  final Uint8List iv;
  final bool success;
  final String? errorMessage;

  EncryptedFileData({
    required this.encryptedData,
    required this.iv,
    required this.success,
    this.errorMessage,
  });
}

class DecryptedFileData {
  final Uint8List decryptedData;
  final bool success;
  final String? errorMessage;

  DecryptedFileData({
    required this.decryptedData,
    required this.success,
    this.errorMessage,
  });
}

class EncryptFileResult {
  final bool success;
  final String? outputFilePath;
  final int? originalFileSize;
  final int? encryptedFileSize;
  final String? errorMessage;

  EncryptFileResult({
    required this.success,
    this.outputFilePath,
    this.originalFileSize,
    this.encryptedFileSize,
    this.errorMessage,
  });
}

class DecryptFileResult {
  final bool success;
  final String? outputFilePath;
  final int? decryptedFileSize;
  final String? errorMessage;

  DecryptFileResult({
    required this.success,
    this.outputFilePath,
    this.decryptedFileSize,
    this.errorMessage,
  });
}
