import 'package:flutter_tts/flutter_tts.dart';
import 'package:echo_cave/services/config_service.dart';

class TTSService {
  static final TTSService _instance = TTSService._internal();
  factory TTSService() => _instance;
  TTSService._internal();

  final FlutterTts _tts = FlutterTts();
  bool _initialized = false;

  Future<void> initialize() async {
    if (_initialized) return;

    final config = ConfigService.instance;
    print('[TTS] 开始初始化...');
    print(
        '[TTS] 使用配置 - 语言: ${config.defaultTtsLanguage}, 语速: ${config.defaultTtsRate}, 音调: ${config.defaultTtsPitch}');

    try {
      print('[TTS] 1/5: 设置语言...');
      await _tts.setLanguage(config.defaultTtsLanguage);

      print('[TTS] 2/5: 设置音调...');
      await _tts.setPitch(config.defaultTtsPitch);

      print('[TTS] 3/5: 设置语速...');
      await _tts.setSpeechRate(config.defaultTtsRate);

      print('[TTS] 4/5: 设置音量...');
      await _tts.setVolume(1.0);

      print('[TTS] 5/5: 配置完成');
    } catch (e) {
      print('[TTS] 配置过程中出现问题，但将继续使用默认设置: $e');
    }

    _initialized = true;
    print('[TTS] 初始化完成');
  }

  Future<void> speak(String text) async {
    await initialize();
    print('[TTS] 开始朗读: $text');
    try {
      await _tts.speak(text).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          print('[TTS] 朗读超时 - 设备可能不支持TTS或需要网络连接');
          throw Exception(
              'TTS timeout - device may not support TTS or requires network');
        },
      );
      print('[TTS] speak() 调用完成');
    } catch (e) {
      print('[TTS] 朗读异常: $e');
      rethrow;
    }
  }

  Future<void> stop() async {
    print('[TTS] 停止朗读');
    await _tts.stop();
  }

  void setCompletionHandler(void Function() onComplete) {
    _tts.setCompletionHandler(onComplete);
  }

  void setStartHandler(void Function() onStart) {
    _tts.setStartHandler(onStart);
  }

  void setErrorHandler(void Function(String) onError) {
    _tts.setErrorHandler((msg) {
      onError(msg);
    });
  }
}
