import 'package:echo_cave/services/xunfei_tts_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class TtsTestView extends HookWidget {
  const TtsTestView({super.key});

  @override
  Widget build(BuildContext context) {
    final textController = useTextEditingController(
        text:
            '我听到了你内心的声音，知道你现在很难过。每个人都会经历低谷，这并不意味着你不够好。你的感受是真实的，也是重要的。请记住，黑暗总会过去，而你比想象中更坚强。我会陪伴在你身边，直到你重新找到内心的光亮。');
    final ttsService = useMemoized(() => XunfeiTtsService(
          appId: 'bdefb6d1',
          apiKey: 'd49bb1c7d89ee13e18bf76b38fc2789c',
          apiSecret: 'NzEyNGRjNWQ3MzI4MjY1ZjA5NmIzODEw',
        ));

    return Scaffold(
      appBar: AppBar(
        title: const Text('AI 陪伴测试'),
        backgroundColor: Colors.blueGrey.shade800,
        foregroundColor: Colors.white,
      ),
      backgroundColor: Colors.grey.shade900,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blueGrey.shade800.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blueGrey.shade600),
              ),
              child: TextField(
                controller: textController,
                decoration: const InputDecoration(
                  labelText: '输入安慰的话语',
                  labelStyle: TextStyle(color: Colors.white70),
                  border: InputBorder.none,
                  hintText: '在这里输入想要测试的安慰话语...',
                  hintStyle: TextStyle(color: Colors.white38),
                ),
                style: const TextStyle(color: Colors.white, fontSize: 16),
                maxLines: 8,
                minLines: 6,
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.volume_up, color: Colors.white),
                label: const Text(
                  '🤗 AI 温柔陪伴',
                  style: TextStyle(fontSize: 18, color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber.shade700,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: () {
                  final text = textController.text;
                  if (text.isNotEmpty) {
                    ttsService.speak(text);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
