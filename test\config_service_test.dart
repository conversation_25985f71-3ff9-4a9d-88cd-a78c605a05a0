import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/config_service.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() {
  group('ConfigService 测试', () {
    setUpAll(() async {
      // 加载测试环境变量
      await dotenv.load(fileName: ".env");
    });

    test('应该能够读取DeepSeek配置', () {
      final config = ConfigService.instance;

      expect(config.deepseekApiKey.isNotEmpty, true);
      expect(config.deepseekBaseUrl, equals('https://api.deepseek.com'));
    });

    test('应该能够读取应用配置', () {
      final config = ConfigService.instance;

      expect(config.appName, equals('Echo Cave'));
      expect(config.appVersion, equals('1.0.0'));
      expect(config.debugMode, equals(true));
    });

    test('应该能够读取TTS配置', () {
      final config = ConfigService.instance;

      expect(config.defaultTtsLanguage, equals('zh-CN'));
      expect(config.defaultTtsRate, equals(0.4));
      expect(config.defaultTtsPitch, equals(1.2));
    });

    test('应该正确检查配置状态', () {
      final config = ConfigService.instance;

      expect(config.isConfigured, true);
    });

    test('应该正确遮蔽API Key', () {
      final config = ConfigService.instance;

      // 打印配置信息不应该抛出异常
      expect(() => config.printConfig(), returnsNormally);
    });
  });
}
