import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../stt/stt_models.dart';
import 'emotion_models.dart';

/// 情感分析服务
class EmotionAnalysisService extends ChangeNotifier {
  // 情感关键词词典
  static const Map<EmotionType, List<String>> _emotionKeywords = {
    EmotionType.sad: [
      '难过', '伤心', '痛苦', '失落', '沮丧', '绝望', '哭', '眼泪', '心痛',
      '悲伤', '忧郁', '低落', '消沉', '失望', '心碎', '孤单', '空虚'
    ],
    EmotionType.anxious: [
      '焦虑', '紧张', '担心', '害怕', '恐惧', '不安', '慌张', '忐忑',
      '压力', '烦躁', '心慌', '紧迫', '惶恐', '不知所措', '手足无措'
    ],
    EmotionType.angry: [
      '愤怒', '生气', '恼火', '愤恨', '暴躁', '火大', '气愤', '恼怒',
      '愤慨', '怒火', '发火', '抓狂', '暴怒', '气死', '讨厌'
    ],
    EmotionType.lonely: [
      '孤独', '寂寞', '孤单', '独自', '一个人', '没人', '空虚', '孤立',
      '被遗弃', '无助', '孤零零', '形单影只', '举目无亲'
    ],
    EmotionType.stressed: [
      '压力', '累', '疲惫', '筋疲力尽', '透支', '崩溃', '撑不住',
      '吃不消', '负担', '重压', '喘不过气', '压抑', '窒息'
    ],
    EmotionType.confused: [
      '困惑', '迷茫', '不知道', '不明白', '搞不懂', '糊涂', '混乱',
      '不清楚', '疑惑', '茫然', '不解', '摸不着头脑'
    ],
    EmotionType.happy: [
      '开心', '快乐', '高兴', '兴奋', '愉快', '欣喜', '喜悦', '满足',
      '幸福', '美好', '棒', '太好了', '开心', '爽', '舒服'
    ],
    EmotionType.hopeful: [
      '希望', '期待', '相信', '坚持', '努力', '加油', '会好的', '有信心',
      '乐观', '积极', '向前', '未来', '梦想', '目标'
    ],
  };

  // 求助信号关键词
  static const List<String> _helpSignals = [
    '帮助', '救救我', '怎么办', '不知道怎么办', '求助', '帮帮我',
    '没办法', '走投无路', '绝路', '完了', '没救了', '救命'
  ];

  // 语音特征分析
  final List<SpeechFeatures> _speechHistory = [];
  final List<EmotionAnalysis> _emotionHistory = [];
  
  // 事件流
  final StreamController<EmotionAnalysis> _emotionController = 
      StreamController<EmotionAnalysis>.broadcast();
  final StreamController<SpeechFeatures> _speechController = 
      StreamController<SpeechFeatures>.broadcast();
  
  // 状态
  DateTime? _lastAnalysisTime;
  DateTime? _sessionStartTime;

  /// 情感分析结果流
  Stream<EmotionAnalysis> get onEmotionAnalysis => _emotionController.stream;
  
  /// 语音特征分析流
  Stream<SpeechFeatures> get onSpeechFeatures => _speechController.stream;
  
  /// 情感历史
  List<EmotionAnalysis> get emotionHistory => List.unmodifiable(_emotionHistory);
  
  /// 语音特征历史
  List<SpeechFeatures> get speechHistory => List.unmodifiable(_speechHistory);
  
  /// 会话是否活跃
  bool get isSessionActive => _sessionStartTime != null;

  /// 开始新的分析会话
  void startSession() {
    _sessionStartTime = DateTime.now();
    _emotionHistory.clear();
    _speechHistory.clear();
    debugPrint('情感分析会话开始');
    notifyListeners();
  }

  /// 结束分析会话
  void endSession() {
    _sessionStartTime = null;
    debugPrint('情感分析会话结束');
    notifyListeners();
  }

  /// 分析STT结果的情感
  Future<EmotionAnalysis> analyzeEmotion(SttResult sttResult) async {
    final now = DateTime.now();
    
    // 分析文本情感
    final emotionAnalysis = _analyzeTextEmotion(sttResult.text, now);
    
    // 分析语音特征
    final speechFeatures = _analyzeSpeechFeatures(sttResult, now);
    
    // 记录历史
    _emotionHistory.add(emotionAnalysis);
    _speechHistory.add(speechFeatures);
    
    // 限制历史记录长度
    if (_emotionHistory.length > 100) {
      _emotionHistory.removeAt(0);
    }
    if (_speechHistory.length > 100) {
      _speechHistory.removeAt(0);
    }
    
    // 发送事件
    _emotionController.add(emotionAnalysis);
    _speechController.add(speechFeatures);
    
    _lastAnalysisTime = now;
    notifyListeners();
    
    debugPrint('情感分析完成: $emotionAnalysis');
    return emotionAnalysis;
  }

  /// 分析文本情感
  EmotionAnalysis _analyzeTextEmotion(String text, DateTime timestamp) {
    final lowerText = text.toLowerCase();
    final detectedKeywords = <String>[];
    final emotionScores = <EmotionType, double>{};
    
    // 检测情感关键词
    for (final entry in _emotionKeywords.entries) {
      final emotionType = entry.key;
      final keywords = entry.value;
      
      double score = 0.0;
      for (final keyword in keywords) {
        if (lowerText.contains(keyword)) {
          detectedKeywords.add(keyword);
          score += 1.0;
        }
      }
      
      if (score > 0) {
        emotionScores[emotionType] = score / keywords.length;
      }
    }
    
    // 检测求助信号
    bool hasHelpSignal = false;
    for (final signal in _helpSignals) {
      if (lowerText.contains(signal)) {
        detectedKeywords.add(signal);
        hasHelpSignal = true;
      }
    }
    
    // 确定主要情感
    EmotionType primaryEmotion = EmotionType.neutral;
    double maxScore = 0.0;
    
    if (emotionScores.isNotEmpty) {
      final maxEntry = emotionScores.entries
          .reduce((a, b) => a.value > b.value ? a : b);
      primaryEmotion = maxEntry.key;
      maxScore = maxEntry.value;
    }
    
    // 计算情感强度
    EmotionIntensity intensity = EmotionIntensity.low;
    if (maxScore > 0.3) intensity = EmotionIntensity.medium;
    if (maxScore > 0.6) intensity = EmotionIntensity.high;
    if (maxScore > 0.8) intensity = EmotionIntensity.extreme;
    
    // 计算情感分数 (-1.0 到 1.0)
    double emotionScore = 0.0;
    if (primaryEmotion == EmotionType.happy || primaryEmotion == EmotionType.hopeful) {
      emotionScore = maxScore;
    } else if (_isNegativeEmotion(primaryEmotion)) {
      emotionScore = -maxScore;
    }
    
    // 如果有求助信号，增强消极情感
    if (hasHelpSignal) {
      emotionScore = min(-0.7, emotionScore - 0.3);
      intensity = EmotionIntensity.high;
    }
    
    // 计算置信度
    double confidence = maxScore;
    if (detectedKeywords.length > 1) {
      confidence = min(1.0, confidence + 0.2);
    }
    
    return EmotionAnalysis(
      primaryEmotion: primaryEmotion,
      intensity: intensity,
      confidence: confidence,
      keywords: detectedKeywords,
      emotionScore: emotionScore,
      timestamp: timestamp,
      originalText: text,
      secondaryEmotions: _getSecondaryEmotions(emotionScores, primaryEmotion),
    );
  }

  /// 分析语音特征
  SpeechFeatures _analyzeSpeechFeatures(SttResult sttResult, DateTime timestamp) {
    // 计算语速 (字符/秒)
    double speechRate = 0.0;
    double pauseDuration = 0.0;
    double continuity = 1.0;
    bool hasPause = false;
    
    if (_lastAnalysisTime != null) {
      final timeDiff = timestamp.difference(_lastAnalysisTime!).inMilliseconds / 1000.0;
      if (timeDiff > 0) {
        speechRate = sttResult.text.length / timeDiff;
        
        // 检测停顿（如果时间间隔过长）
        if (timeDiff > 1.5) {
          hasPause = true;
          pauseDuration = timeDiff;
          continuity = max(0.0, 1.0 - (timeDiff / 10.0));
        }
      }
    }
    
    return SpeechFeatures(
      speechRate: speechRate,
      pauseDuration: pauseDuration,
      continuity: continuity,
      hasPause: hasPause,
      segmentDuration: _lastAnalysisTime != null 
          ? timestamp.difference(_lastAnalysisTime!).inMilliseconds / 1000.0 
          : 0.0,
      timestamp: timestamp,
    );
  }

  /// 判断是否为消极情感
  bool _isNegativeEmotion(EmotionType emotion) {
    return [
      EmotionType.sad,
      EmotionType.anxious,
      EmotionType.angry,
      EmotionType.lonely,
      EmotionType.stressed,
      EmotionType.confused,
      EmotionType.fearful,
    ].contains(emotion);
  }

  /// 获取次要情感
  List<EmotionType> _getSecondaryEmotions(
    Map<EmotionType, double> emotionScores,
    EmotionType primaryEmotion,
  ) {
    return emotionScores.entries
        .where((entry) => entry.key != primaryEmotion && entry.value > 0.2)
        .map((entry) => entry.key)
        .take(2)
        .toList();
  }

  /// 获取情感趋势
  EmotionType? getEmotionTrend({int windowSize = 5}) {
    if (_emotionHistory.length < windowSize) return null;
    
    final recentEmotions = _emotionHistory
        .skip(_emotionHistory.length - windowSize)
        .map((e) => e.primaryEmotion)
        .toList();
    
    final emotionCounts = <EmotionType, int>{};
    for (final emotion in recentEmotions) {
      emotionCounts[emotion] = (emotionCounts[emotion] ?? 0) + 1;
    }
    
    return emotionCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// 获取平均情感分数
  double getAverageEmotionScore({int windowSize = 10}) {
    if (_emotionHistory.isEmpty) return 0.0;
    
    final recentAnalyses = _emotionHistory.length > windowSize
        ? _emotionHistory.skip(_emotionHistory.length - windowSize)
        : _emotionHistory;
    
    final scores = recentAnalyses.map((e) => e.emotionScore).toList();
    return scores.reduce((a, b) => a + b) / scores.length;
  }

  /// 检测情感变化
  bool hasEmotionChanged({double threshold = 0.3}) {
    if (_emotionHistory.length < 2) return false;
    
    final current = _emotionHistory.last.emotionScore;
    final previous = _emotionHistory[_emotionHistory.length - 2].emotionScore;
    
    return (current - previous).abs() > threshold;
  }

  /// 清除历史记录
  void clearHistory() {
    _emotionHistory.clear();
    _speechHistory.clear();
    _lastAnalysisTime = null;
    notifyListeners();
    debugPrint('情感分析历史已清除');
  }

  @override
  void dispose() {
    _emotionController.close();
    _speechController.close();
    super.dispose();
  }
}
