import 'package:flutter/material.dart';

/// 语音波形可视化组件
class VoiceWaveform extends StatefulWidget {
  final bool isRecording;
  final double amplitude;
  final Color color;
  final double height;
  final int barCount;

  const VoiceWaveform({
    super.key,
    required this.isRecording,
    this.amplitude = 0.5,
    this.color = Colors.tealAccent,
    this.height = 60.0,
    this.barCount = 20,
  });

  @override
  State<VoiceWaveform> createState() => _VoiceWaveformState();
}

class _VoiceWaveformState extends State<VoiceWaveform>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<AnimationController> _barControllers;
  late List<Animation<double>> _barAnimations;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    // 为每个波形条创建独立的动画控制器
    _barControllers = List.generate(
      widget.barCount,
      (index) => AnimationController(
        duration: Duration(milliseconds: 300 + (index * 50) % 500),
        vsync: this,
      ),
    );

    _barAnimations = _barControllers.map((controller) {
      return Tween<double>(begin: 0.1, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimation();
  }

  void _startAnimation() {
    if (widget.isRecording) {
      // 启动所有波形条的动画
      for (int i = 0; i < _barControllers.length; i++) {
        Future.delayed(Duration(milliseconds: i * 100), () {
          if (mounted && widget.isRecording) {
            _barControllers[i].repeat(reverse: true);
          }
        });
      }
    }
  }

  @override
  void didUpdateWidget(VoiceWaveform oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isRecording != oldWidget.isRecording) {
      if (widget.isRecording) {
        _startAnimation();
      } else {
        // 停止所有动画
        for (final controller in _barControllers) {
          controller.stop();
          controller.reset();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(widget.barCount, (index) {
          return AnimatedBuilder(
            animation: _barAnimations[index],
            builder: (context, child) {
              final barHeight = widget.isRecording
                  ? widget.height * _barAnimations[index].value * widget.amplitude
                  : 4.0;
              
              return Container(
                width: 3.0,
                height: barHeight,
                margin: const EdgeInsets.symmetric(horizontal: 1.0),
                decoration: BoxDecoration(
                  color: widget.color.withValues(
                    alpha: widget.isRecording ? 0.8 : 0.3,
                  ),
                  borderRadius: BorderRadius.circular(1.5),
                ),
              );
            },
          );
        }),
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _barControllers) {
      controller.dispose();
    }
    super.dispose();
  }
}

/// 圆形语音波形组件
class CircularVoiceWaveform extends StatefulWidget {
  final bool isRecording;
  final double amplitude;
  final Color color;
  final double size;
  final int waveCount;

  const CircularVoiceWaveform({
    super.key,
    required this.isRecording,
    this.amplitude = 0.5,
    this.color = Colors.tealAccent,
    this.size = 120.0,
    this.waveCount = 3,
  });

  @override
  State<CircularVoiceWaveform> createState() => _CircularVoiceWaveformState();
}

class _CircularVoiceWaveformState extends State<CircularVoiceWaveform>
    with TickerProviderStateMixin {
  late List<AnimationController> _waveControllers;
  late List<Animation<double>> _waveAnimations;

  @override
  void initState() {
    super.initState();
    
    _waveControllers = List.generate(
      widget.waveCount,
      (index) => AnimationController(
        duration: Duration(milliseconds: 1500 + index * 300),
        vsync: this,
      ),
    );

    _waveAnimations = _waveControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOut),
      );
    }).toList();

    _startWaveAnimation();
  }

  void _startWaveAnimation() {
    if (widget.isRecording) {
      for (int i = 0; i < _waveControllers.length; i++) {
        Future.delayed(Duration(milliseconds: i * 200), () {
          if (mounted && widget.isRecording) {
            _waveControllers[i].repeat();
          }
        });
      }
    }
  }

  @override
  void didUpdateWidget(CircularVoiceWaveform oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isRecording != oldWidget.isRecording) {
      if (widget.isRecording) {
        _startWaveAnimation();
      } else {
        for (final controller in _waveControllers) {
          controller.stop();
          controller.reset();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: Listenable.merge(_waveControllers),
        builder: (context, child) {
          return CustomPaint(
            painter: CircularWavePainter(
              waveAnimations: _waveAnimations,
              color: widget.color,
              amplitude: widget.amplitude,
              isRecording: widget.isRecording,
            ),
            size: Size(widget.size, widget.size),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    for (final controller in _waveControllers) {
      controller.dispose();
    }
    super.dispose();
  }
}

/// 圆形波形绘制器
class CircularWavePainter extends CustomPainter {
  final List<Animation<double>> waveAnimations;
  final Color color;
  final double amplitude;
  final bool isRecording;

  CircularWavePainter({
    required this.waveAnimations,
    required this.color,
    required this.amplitude,
    required this.isRecording,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width / 6;

    // 绘制中心圆点
    final centerPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, baseRadius * 0.3, centerPaint);

    if (!isRecording) return;

    // 绘制波形圆圈
    for (int i = 0; i < waveAnimations.length; i++) {
      final waveValue = waveAnimations[i].value;
      final radius = baseRadius + (waveValue * baseRadius * 2 * amplitude);
      final alpha = (1.0 - waveValue) * 0.6;

      final wavePaint = Paint()
        ..color = color.withValues(alpha: alpha)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      canvas.drawCircle(center, radius, wavePaint);
    }
  }

  @override
  bool shouldRepaint(CircularWavePainter oldDelegate) {
    return oldDelegate.isRecording != isRecording ||
           oldDelegate.amplitude != amplitude;
  }
}

/// 语音活动指示器
class VoiceActivityIndicator extends StatefulWidget {
  final bool isActive;
  final double intensity;
  final Color color;

  const VoiceActivityIndicator({
    super.key,
    required this.isActive,
    this.intensity = 0.5,
    this.color = Colors.green,
  });

  @override
  State<VoiceActivityIndicator> createState() => _VoiceActivityIndicatorState();
}

class _VoiceActivityIndicatorState extends State<VoiceActivityIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(VoiceActivityIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: 12.0,
          height: 12.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: widget.color.withValues(alpha: _animation.value),
            boxShadow: widget.isActive
                ? [
                    BoxShadow(
                      color: widget.color.withValues(alpha: 0.5),
                      blurRadius: 8.0 * _animation.value,
                      spreadRadius: 2.0 * _animation.value,
                    ),
                  ]
                : null,
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
