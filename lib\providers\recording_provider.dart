import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/audio_recording_service.dart';
import 'dart:io';
import '../services/encryption_service.dart';
import '../data/secure_storage_service.dart';

// Recording states
enum RecordingState {
  idle, // Not recording, ready to start
  recording, // Currently recording
  stopping, // In the process of stopping
  error, // Error occurred
  encrypting, // In the process of encrypting
}

// Recording state data
class RecordingData {
  final RecordingState state;
  final String? errorMessage;
  final String? lastRecordingPath;

  const RecordingData({
    required this.state,
    this.errorMessage,
    this.lastRecordingPath,
  });

  RecordingData copyWith({
    RecordingState? state,
    String? errorMessage,
    String? lastRecordingPath,
  }) {
    return RecordingData(
      state: state ?? this.state,
      errorMessage: errorMessage,
      lastRecordingPath: lastRecordingPath ?? this.lastRecordingPath,
    );
  }
}

// Recording provider
class RecordingNotifier extends StateNotifier<RecordingData> {
  final AudioRecordingService _audioService = AudioRecordingService();
  final Ref _ref;

  RecordingNotifier(this._ref)
      : super(const RecordingData(state: RecordingState.idle));

  // Start recording
  Future<void> startRecording() async {
    if (state.state == RecordingState.recording) {
      return; // Already recording
    }

    state = state.copyWith(state: RecordingState.recording, errorMessage: null);

    try {
      final success = await _audioService.startRecording();
      if (!success) {
        state = state.copyWith(
          state: RecordingState.error,
          errorMessage: 'Failed to start recording',
        );
      }
    } catch (e) {
      state = state.copyWith(
        state: RecordingState.error,
        errorMessage: 'Error starting recording: $e',
      );
    }
  }

  // Stop recording
  Future<void> stopRecording() async {
    if (state.state != RecordingState.recording) {
      return; // Not recording
    }

    state = state.copyWith(state: RecordingState.stopping);

    try {
      final recordingPath = await _audioService.stopRecording();
      if (recordingPath != null) {
        // --- Start Encryption Flow ---
        state = state.copyWith(state: RecordingState.encrypting);

        final secureStorage = _ref.read(secureStorageServiceProvider);
        final encryptionKey = await secureStorage.getEncryptionKey();

        if (encryptionKey == null) {
          throw Exception('Encryption key not found');
        }

        final encryptedFilePath = recordingPath.replaceFirst('.aac', '.cave');
        final result = await EncryptionService.encryptFile(
            recordingPath, encryptedFilePath, encryptionKey);

        // Clean up original file
        try {
          final originalFile = File(recordingPath);
          if (await originalFile.exists()) {
            await originalFile.delete();
          }
        } catch (e) {
          // Log cleanup error, but don't fail the whole operation
          print('Failed to delete original recording file: $e');
        }

        if (result.success) {
          state = state.copyWith(
            state: RecordingState.idle,
            lastRecordingPath: result.outputFilePath,
          );
        } else {
          state = state.copyWith(
            state: RecordingState.error,
            errorMessage: result.errorMessage ?? 'Encryption failed',
          );
        }
        // --- End Encryption Flow ---
      } else {
        state = state.copyWith(
          state: RecordingState.error,
          errorMessage: 'Failed to stop recording',
        );
      }
    } catch (e) {
      state = state.copyWith(
        state: RecordingState.error,
        errorMessage: 'Error stopping recording: $e',
      );
    }
  }

  // Dispose the service
  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}

// Provider
final recordingProvider =
    StateNotifierProvider<RecordingNotifier, RecordingData>(
  (ref) => RecordingNotifier(ref),
);
