import 'dart:math';
import 'package:flutter/material.dart';
import '../services/empathy/emotion_models.dart';

/// 情感可视化组件
class EmotionVisualization extends StatefulWidget {
  final EmotionAnalysis? currentEmotion;
  final List<EmpathyTrigger> recentTriggers;
  final bool isListening;
  final double size;

  const EmotionVisualization({
    super.key,
    this.currentEmotion,
    this.recentTriggers = const [],
    this.isListening = false,
    this.size = 200.0,
  });

  @override
  State<EmotionVisualization> createState() => _EmotionVisualizationState();
}

class _EmotionVisualizationState extends State<EmotionVisualization>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rippleController;
  late AnimationController _emotionController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _rippleAnimation;
  late Animation<double> _emotionAnimation;

  @override
  void initState() {
    super.initState();
    
    // 脉冲动画（心跳效果）
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    // 涟漪动画（触发效果）
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));
    
    // 情感变化动画
    _emotionController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _emotionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _emotionController,
      curve: Curves.elasticOut,
    ));

    // 开始脉冲动画
    _startPulseAnimation();
  }

  @override
  void didUpdateWidget(EmotionVisualization oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 检测情感变化
    if (widget.currentEmotion != oldWidget.currentEmotion) {
      _emotionController.forward(from: 0.0);
    }
    
    // 检测新的触发
    if (widget.recentTriggers.length > oldWidget.recentTriggers.length) {
      _triggerRippleEffect();
    }
    
    // 检测监听状态变化
    if (widget.isListening != oldWidget.isListening) {
      if (widget.isListening) {
        _startPulseAnimation();
      } else {
        _pulseController.stop();
      }
    }
  }

  void _startPulseAnimation() {
    if (widget.isListening) {
      _pulseController.repeat(reverse: true);
    }
  }

  void _triggerRippleEffect() {
    _rippleController.forward(from: 0.0);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _pulseController,
          _rippleController,
          _emotionController,
        ]),
        builder: (context, child) {
          return CustomPaint(
            painter: EmotionPainter(
              emotion: widget.currentEmotion,
              pulseValue: _pulseAnimation.value,
              rippleValue: _rippleAnimation.value,
              emotionValue: _emotionAnimation.value,
              isListening: widget.isListening,
              recentTriggers: widget.recentTriggers,
            ),
            size: Size(widget.size, widget.size),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rippleController.dispose();
    _emotionController.dispose();
    super.dispose();
  }
}

/// 情感绘制器
class EmotionPainter extends CustomPainter {
  final EmotionAnalysis? emotion;
  final double pulseValue;
  final double rippleValue;
  final double emotionValue;
  final bool isListening;
  final List<EmpathyTrigger> recentTriggers;

  EmotionPainter({
    this.emotion,
    required this.pulseValue,
    required this.rippleValue,
    required this.emotionValue,
    required this.isListening,
    required this.recentTriggers,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width / 4;

    // 绘制背景圆圈
    _drawBackground(canvas, center, baseRadius);
    
    // 绘制脉冲效果
    if (isListening) {
      _drawPulse(canvas, center, baseRadius);
    }
    
    // 绘制涟漪效果
    if (rippleValue > 0) {
      _drawRipple(canvas, center, baseRadius);
    }
    
    // 绘制情感核心
    _drawEmotionCore(canvas, center, baseRadius);
    
    // 绘制情感粒子
    if (emotion != null) {
      _drawEmotionParticles(canvas, center, baseRadius);
    }
    
    // 绘制触发指示器
    _drawTriggerIndicators(canvas, center, baseRadius);
  }

  void _drawBackground(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = const Color(0xFF1A1A2E).withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius * 2, paint);
  }

  void _drawPulse(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = _getEmotionColor().withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    final pulseRadius = radius * pulseValue;
    canvas.drawCircle(center, pulseRadius, paint);
  }

  void _drawRipple(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: (1.0 - rippleValue) * 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;
    
    final rippleRadius = radius * (1.0 + rippleValue * 2.0);
    canvas.drawCircle(center, rippleRadius, paint);
  }

  void _drawEmotionCore(Canvas canvas, Offset center, double radius) {
    final emotionColor = _getEmotionColor();
    final intensity = _getEmotionIntensity();
    
    // 核心圆圈
    final corePaint = Paint()
      ..color = emotionColor.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    final coreRadius = radius * (0.6 + intensity * 0.4) * emotionValue;
    canvas.drawCircle(center, coreRadius, corePaint);
    
    // 内部光晕
    final glowPaint = Paint()
      ..color = emotionColor.withValues(alpha: 0.4)
      ..style = PaintingStyle.fill;
    
    final glowRadius = coreRadius * 1.3;
    canvas.drawCircle(center, glowRadius, glowPaint);
  }

  void _drawEmotionParticles(Canvas canvas, Offset center, double radius) {
    if (emotion == null) return;
    
    final particleCount = (emotion!.confidence * 20).round();
    final emotionColor = _getEmotionColor();
    
    for (int i = 0; i < particleCount; i++) {
      final angle = (i / particleCount) * 2 * pi;
      final distance = radius * (0.8 + sin(emotionValue * pi) * 0.3);
      
      final particleX = center.dx + cos(angle) * distance;
      final particleY = center.dy + sin(angle) * distance;
      
      final particlePaint = Paint()
        ..color = emotionColor.withValues(alpha: 0.6)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(particleX, particleY),
        2.0 * emotionValue,
        particlePaint,
      );
    }
  }

  void _drawTriggerIndicators(Canvas canvas, Offset center, double radius) {
    if (recentTriggers.isEmpty) return;
    
    final now = DateTime.now();
    final validTriggers = recentTriggers.where((trigger) {
      final age = now.difference(trigger.timestamp).inSeconds;
      return age < 10; // 只显示10秒内的触发
    }).toList();
    
    for (int i = 0; i < validTriggers.length && i < 5; i++) {
      final trigger = validTriggers[i];
      final age = now.difference(trigger.timestamp).inSeconds;
      final alpha = (1.0 - age / 10.0).clamp(0.0, 1.0);
      
      final angle = (i / 5) * 2 * pi;
      final distance = radius * 1.5;
      
      final indicatorX = center.dx + cos(angle) * distance;
      final indicatorY = center.dy + sin(angle) * distance;
      
      final indicatorPaint = Paint()
        ..color = _getTriggerColor(trigger.type).withValues(alpha: alpha)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(indicatorX, indicatorY),
        4.0 * trigger.strength,
        indicatorPaint,
      );
    }
  }

  Color _getEmotionColor() {
    if (emotion == null) return Colors.grey;
    
    const emotionColors = {
      EmotionType.sad: Colors.blue,
      EmotionType.anxious: Colors.orange,
      EmotionType.angry: Colors.red,
      EmotionType.lonely: Colors.purple,
      EmotionType.stressed: Colors.deepOrange,
      EmotionType.confused: Colors.amber,
      EmotionType.happy: Colors.green,
      EmotionType.hopeful: Colors.lightGreen,
      EmotionType.fearful: Colors.indigo,
      EmotionType.neutral: Colors.grey,
      EmotionType.positive: Colors.cyan,
      EmotionType.negative: Colors.redAccent,
    };
    
    return emotionColors[emotion!.primaryEmotion] ?? Colors.grey;
  }

  double _getEmotionIntensity() {
    if (emotion == null) return 0.0;
    
    switch (emotion!.intensity) {
      case EmotionIntensity.low:
        return 0.3;
      case EmotionIntensity.medium:
        return 0.6;
      case EmotionIntensity.high:
        return 0.8;
      case EmotionIntensity.extreme:
        return 1.0;
    }
  }

  Color _getTriggerColor(EmpathyTriggerType type) {
    const triggerColors = {
      EmpathyTriggerType.emotionalKeyword: Colors.yellow,
      EmpathyTriggerType.speechRateAnomaly: Colors.orange,
      EmpathyTriggerType.longPause: Colors.blue,
      EmpathyTriggerType.emotionIntensity: Colors.red,
      EmpathyTriggerType.repetitiveExpression: Colors.purple,
      EmpathyTriggerType.helpSignal: Colors.pink,
      EmpathyTriggerType.emotionTransition: Colors.cyan,
    };
    
    return triggerColors[type] ?? Colors.white;
  }

  @override
  bool shouldRepaint(EmotionPainter oldDelegate) {
    return oldDelegate.emotion != emotion ||
           oldDelegate.pulseValue != pulseValue ||
           oldDelegate.rippleValue != rippleValue ||
           oldDelegate.emotionValue != emotionValue ||
           oldDelegate.isListening != isListening ||
           oldDelegate.recentTriggers.length != recentTriggers.length;
  }
}
