import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:echo_cave/models/conversation_model.dart';
import 'package:echo_cave/services/deepseek_conversation_service.dart';
import 'package:echo_cave/services/tts_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AIConversationView extends HookConsumerWidget {
  const AIConversationView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final messages = useState<List<Message>>([]);
    final isProcessing = useState(false);
    final textController = useTextEditingController();
    final scrollController = useScrollController();

    // 初始化服务
    final deepseekService = useMemoized(
      () => DeepSeekConversationService(const FlutterSecureStorage()),
      [],
    );
    final ttsService = useMemoized(() => TTSService(), []);

    // 添加初始欢迎消息
    useEffect(() {
      if (messages.value.isEmpty) {
        messages.value = [
          Message(
            id: '0',
            content: '你好，我是你的陪伴者。无论你想分享什么，我都会用心倾听。💙',
            isFromUser: false,
            timestamp: DateTime.now(),
          ),
        ];
      }
      return null;
    }, []);

    // 自动滚动到底部
    void scrollToBottom() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (scrollController.hasClients) {
          scrollController.animateTo(
            scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }

    // 发送消息
    Future<void> sendMessage(String content) async {
      if (content.trim().isEmpty || isProcessing.value) return;

      // 添加用户消息
      final userMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: content.trim(),
        isFromUser: true,
        timestamp: DateTime.now(),
      );

      messages.value = [...messages.value, userMessage];
      textController.clear();
      scrollToBottom();

      // 开始处理
      isProcessing.value = true;

      try {
        // 获取AI回复
        final aiResponse = await deepseekService.getEmpathicResponse(content);

        // 添加AI消息
        final aiMessage = Message(
          id: (DateTime.now().millisecondsSinceEpoch + 1).toString(),
          content: aiResponse,
          isFromUser: false,
          timestamp: DateTime.now(),
        );

        messages.value = [...messages.value, aiMessage];
        scrollToBottom();

        // 朗读AI回复
        try {
          await ttsService.speak(aiResponse);
        } catch (e) {
          print('TTS Error: $e');
          // TTS失败不影响对话继续
        }
      } catch (e) {
        print('AI Response Error: $e');
        // 添加错误回复
        final errorMessage = Message(
          id: (DateTime.now().millisecondsSinceEpoch + 1).toString(),
          content: '抱歉，我现在无法回应。但我依然在这里陪伴着你。💙',
          isFromUser: false,
          timestamp: DateTime.now(),
        );
        messages.value = [...messages.value, errorMessage];
        scrollToBottom();
      } finally {
        isProcessing.value = false;
      }
    }

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          '🤖 AI陪伴',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Column(
        children: [
          // 对话区域
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black,
                    Colors.grey.shade900,
                  ],
                ),
              ),
              child: ListView.builder(
                controller: scrollController,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                itemCount: messages.value.length,
                itemBuilder: (context, index) {
                  final message = messages.value[index];
                  return MessageBubble(message: message);
                },
              ),
            ),
          ),

          // 处理状态指示器
          if (isProcessing.value)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.amber.shade300,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'AI正在思考中...',
                    style: TextStyle(
                      color: Colors.amber.shade300,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

          // 输入区域
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade900,
              border: Border(
                top: BorderSide(color: Colors.grey.shade700),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: textController,
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: '分享你的感受...',
                      hintStyle: TextStyle(color: Colors.grey.shade500),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: Colors.grey.shade600),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: Colors.grey.shade600),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: Colors.amber.shade300),
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade800,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    maxLines: null,
                    enabled: !isProcessing.value,
                    onSubmitted: sendMessage,
                  ),
                ),
                const SizedBox(width: 8),
                // 发送按钮
                Container(
                  decoration: BoxDecoration(
                    color: Colors.amber.shade300,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.send),
                    color: Colors.black,
                    onPressed: isProcessing.value
                        ? null
                        : () => sendMessage(textController.text),
                  ),
                ),
                const SizedBox(width: 8),
                // 语音输入按钮（预留）
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade700,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.mic),
                    color: Colors.white,
                    onPressed: () {
                      // TODO: 实现语音输入功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('语音输入功能即将推出'),
                          backgroundColor: Colors.grey,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class MessageBubble extends StatelessWidget {
  final Message message;

  const MessageBubble({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    final isUser = message.isFromUser;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.amber.shade300,
              child: const Icon(
                Icons.smart_toy,
                size: 18,
                color: Colors.black,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isUser ? Colors.blue.shade600 : Colors.grey.shade800,
                borderRadius: BorderRadius.circular(16).copyWith(
                  bottomLeft: isUser ? const Radius.circular(16) : Radius.zero,
                  bottomRight: isUser ? Radius.zero : const Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${message.timestamp.hour.toString().padLeft(2, '0')}:${message.timestamp.minute.toString().padLeft(2, '0')}',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue.shade600,
              child: const Icon(
                Icons.person,
                size: 18,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
