{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Program Files/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-7dd7d0553079f52153f7.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-5f532b48ffd8692768c2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-8f3645b4fc1dc82c37c2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-5f532b48ffd8692768c2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-8f3645b4fc1dc82c37c2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-7dd7d0553079f52153f7.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}