import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';

class XunfeiTtsService {
  final String appId;
  final String apiKey;
  final String apiSecret;
  WebSocketChannel? _channel;
  final AudioPlayer _audioPlayer = AudioPlayer();
  final BytesBuilder _audioBuffer = BytesBuilder();

  XunfeiTtsService({
    required this.appId,
    required this.apiKey,
    required this.apiSecret,
  });

  Future<void> speak(String text,
      {String voice = "x4_lingxiaoying_em_v2",
      int speed = 50,
      int pitch = 50}) async {
    try {
      final url = _generateAuthUrl();
      _channel = WebSocketChannel.connect(Uri.parse(url));

      _channel!.sink.add(jsonEncode({
        "common": {"app_id": appId},
        "business": {
          "aue": "lame",
          "tte": "utf8",
          "vcn": voice,
          "speed": speed,
          "pitch": pitch,
          "ent": "intp65"
        },
        "data": {
          "status": 2,
          "text": base64.encode(utf8.encode(text)),
        }
      }));

      _channel!.stream.listen((message) async {
        final Map<String, dynamic> resp = jsonDecode(message);
        if (resp["code"] != 0) {
          print("TTS error: ${resp["message"]}");
          _channel?.sink.close();
          return;
        }
        if (resp["data"]?["audio"] != null) {
          final audio = base64.decode(resp["data"]["audio"]);
          _audioBuffer.add(audio);
        }
        if (resp["data"]?["status"] == 2) {
          await _playAudioFromBuffer();
          _audioBuffer.clear();
          _channel?.sink.close();
        }
      });
    } catch (e) {
      print("TTS speak error: $e");
    }
  }

  Future<void> _playAudioFromBuffer() async {
    try {
      final audioBytes = _audioBuffer.toBytes();
      if (audioBytes.isEmpty) {
        print("No audio data to play");
        return;
      }

      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
          '${tempDir.path}/tts_audio_${DateTime.now().millisecondsSinceEpoch}.mp3');

      await tempFile.writeAsBytes(audioBytes);

      await _audioPlayer.play(DeviceFileSource(tempFile.path));

      print("Audio file saved and played: ${tempFile.path}");
    } catch (e) {
      print("Audio playback error: $e");
    }
  }

  String _generateAuthUrl() {
    final date = HttpDate.format(DateTime.now().toUtc());
    final signatureOrigin =
        "host: tts-api.xfyun.cn\ndate: $date\nGET /v2/tts HTTP/1.1";
    final hmacSha256 = Hmac(sha256, utf8.encode(apiSecret));
    final signatureSha = hmacSha256.convert(utf8.encode(signatureOrigin));
    final signature = base64.encode(signatureSha.bytes);

    final authorization =
        'api_key="$apiKey", algorithm="hmac-sha256", headers="host date request-line", signature="$signature"';
    final authBase64 = base64.encode(utf8.encode(authorization));

    return "wss://tts-api.xfyun.cn/v2/tts?authorization=$authBase64&date=${Uri.encodeComponent(date)}&host=tts-api.xfyun.cn";
  }

  Future<void> dispose() async {
    await _audioPlayer.dispose();
    _channel?.sink.close();
  }
}
