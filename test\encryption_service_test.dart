import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/services/encryption_service.dart';

void main() {
  group('EncryptionService Tests', () {
    late String testKey;
    late Uint8List testData;

    setUpAll(() async {
      // Generate a test encryption key using the same method as our app
      final random = Random.secure();
      final keyBytes = List<int>.generate(32, (_) => random.nextInt(256));
      testKey = base64.encode(keyBytes);

      // Create some test data
      testData = Uint8List.fromList(
        'This is test audio data that will be encrypted'.codeUnits,
      );
    });

    test('should encrypt and decrypt data correctly', () {
      // Encrypt the test data
      final encryptionResult =
          EncryptionService.encryptFileData(testData, testKey);

      expect(encryptionResult.success, isTrue);
      expect(encryptionResult.encryptedData.isNotEmpty, isTrue);
      expect(encryptionResult.iv.length, equals(16)); // IV should be 16 bytes

      // Encrypted data should be different from original
      expect(encryptionResult.encryptedData, isNot(equals(testData)));

      // Decrypt the data
      final decryptionResult = EncryptionService.decryptFileData(
        encryptionResult.encryptedData,
        encryptionResult.iv,
        testKey,
      );

      expect(decryptionResult.success, isTrue);
      expect(decryptionResult.decryptedData, equals(testData));
    });

    test('should fail decryption with wrong key', () {
      // Encrypt with one key
      final encryptionResult =
          EncryptionService.encryptFileData(testData, testKey);
      expect(encryptionResult.success, isTrue);

      // Try to decrypt with a different key
      final wrongKey = base64.encode(List.generate(32, (index) => index % 256));
      final decryptionResult = EncryptionService.decryptFileData(
        encryptionResult.encryptedData,
        encryptionResult.iv,
        wrongKey,
      );

      expect(decryptionResult.success, isFalse);
      expect(decryptionResult.errorMessage, isNotNull);
    });

    test('should fail decryption with wrong IV', () {
      // Encrypt the data
      final encryptionResult =
          EncryptionService.encryptFileData(testData, testKey);
      expect(encryptionResult.success, isTrue);

      // Try to decrypt with a different IV
      final wrongIV = Uint8List.fromList(List.generate(16, (index) => index));
      final decryptionResult = EncryptionService.decryptFileData(
        encryptionResult.encryptedData,
        wrongIV,
        testKey,
      );

      expect(decryptionResult.success, isFalse);
      expect(decryptionResult.errorMessage, isNotNull);
    });

    test('should generate consistent file hashes', () {
      final hash1 = EncryptionService.generateFileHash(testData);
      final hash2 = EncryptionService.generateFileHash(testData);

      expect(hash1, equals(hash2));
      expect(hash1.length, equals(64)); // SHA-256 hash should be 64 characters

      // Different data should produce different hashes
      final differentData = Uint8List.fromList('Different data'.codeUnits);
      final differentHash = EncryptionService.generateFileHash(differentData);
      expect(hash1, isNot(equals(differentHash)));
    });

    test('should handle empty data', () {
      final emptyData = Uint8List(0);

      final encryptionResult =
          EncryptionService.encryptFileData(emptyData, testKey);
      expect(encryptionResult.success, isTrue);

      final decryptionResult = EncryptionService.decryptFileData(
        encryptionResult.encryptedData,
        encryptionResult.iv,
        testKey,
      );

      expect(decryptionResult.success, isTrue);
      expect(decryptionResult.decryptedData, equals(emptyData));
    });

    test('should handle large data', () {
      // Create larger test data (100KB)
      final largeData = Uint8List.fromList(
        List.generate(100 * 1024, (index) => index % 256),
      );

      final encryptionResult =
          EncryptionService.encryptFileData(largeData, testKey);
      expect(encryptionResult.success, isTrue);

      final decryptionResult = EncryptionService.decryptFileData(
        encryptionResult.encryptedData,
        encryptionResult.iv,
        testKey,
      );

      expect(decryptionResult.success, isTrue);
      expect(decryptionResult.decryptedData, equals(largeData));
    });

    test('should fail with invalid base64 key', () {
      const invalidKey = 'This is not a valid base64 key!';

      final encryptionResult =
          EncryptionService.encryptFileData(testData, invalidKey);
      expect(encryptionResult.success, isFalse);
      expect(encryptionResult.errorMessage, isNotNull);
    });
  });
}
