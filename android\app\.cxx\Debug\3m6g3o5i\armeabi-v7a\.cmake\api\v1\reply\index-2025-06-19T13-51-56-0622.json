{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Program Files/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-0243c1e84727489667d6.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-b10246e62c4f6e9d5dcb.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1c30df9cc4851475d1c6.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-b10246e62c4f6e9d5dcb.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-1c30df9cc4851475d1c6.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-0243c1e84727489667d6.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}