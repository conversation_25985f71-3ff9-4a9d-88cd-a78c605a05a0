{"models": {"main": {"provider": "openrouter", "modelId": "google/gemini-2.5-pro-preview", "maxTokens": 64000, "temperature": 0.2}, "research": {"provider": "perplexity", "modelId": "sonar-pro", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "anthropic", "modelId": "claude-4-sonnet", "maxTokens": 64000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Task Master", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "userId": "**********", "defaultTag": "master"}}