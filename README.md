# Echo Cave - 匿名树洞 App

## 1. 核心理念 (Core Philosophy)

一款极致匿名、安全、专注即时情绪释放的语音树洞应用。

- **极致匿名 (Extreme Anonymity)**: 无需注册登录，无用户系统，无社交网络。
- **数据安全 (Data Security)**: 所有语音数据均加密存储在用户本地设备，通过生物识别或密码访问App。
- **即时释放 (Instant Release)**: 交互设计以"用完即走"为核心，最大程度降低用户操作和思考负担，实现随时随地的情绪抒发。

## 2. 功能设计 (Feature Design)

### 2.1. 核心交互：语音倾诉

- **长按图标录音 (App Icon Shortcut)**: 在手机桌面长按App图标，直接唤醒录音功能，实现"抓起即说"。
- **首页即录音 (Homepage is Recorder)**: 打开App即为"树洞"主界面，背景是动态的树木。点击树干或树叶等核心元素，立即开始录音。
- **沉浸式录音 (Immersive Recording)**: 录音界面模拟树洞内部视角，语音波形以水波纹或回声的形式在洞中扩散。
- **录完即走 (Record and Go)**: 录音结束自动加密存档，无"保存"、"发布"等多余按钮。

### 2.2. 数据管理：手势化与本地化

- **无"我的"页面 (No "Profile" Page)**: 彻底移除个人中心，强化纯粹的工具属性。
- **手势操作 (Gesture Control)**:
    - **左滑**: 删除单条语音记录。
    - **右滑**: 为情绪打上简单的标签（如不同颜色的叶子标记）。
    - **双击树洞**: 触发"清空所有记录"的确认提示。

### 2.3. 氛围设计：自然与情感共鸣

- **情绪天气 (Emotional Weather)**: (高级功能) App首页背景可根据近期语音的情绪关键词，动态变换为不同天气效果（如愤怒-雷雨，悲伤-阴天）。
- **视觉反馈 (Visual Feedback)**: 倾诉后，树洞周围会长出象征情绪的植物（如烦躁-带刺藤蔓），当情绪被治愈（如后续录制了积极内容），植物会开出花朵。
- **自然背景音 (Ambient Sound)**: 内置多种白噪音和自然声（篝火、溪流、风声），用户可自由切换，营造安全、放松的倾诉环境。

## 3. 交互设计巧思 (Creative Interaction Design)

交互设计完全可以跳出传统框架，用更贴合 "树洞" 氛围的方式呈现～分享几个巧思方向：

🌳 **打破 Tab 栏，用「场景化入口」替代**
- **长按树洞图标触发录音**: 把 APP 图标设计成树洞形状，在桌面长按直接唤醒语音录制，省去打开 APP 再找入口的步骤，就像 "抓起电话就吐槽" 一样本能；
- **首页即 "树洞界面"**: 打开 APP 没有复杂菜单，直接是一棵动态树的背景，点击树干 / 树叶就弹出语音按钮，录音时树叶会随声音摆动，氛围感拉满；
- **"情绪天气" 动态首页**: 根据用户近期倾诉的情绪关键词，首页背景自动切换天气（比如愤怒是雷雨，委屈是阴天），点击 "打雷""下雨" 就能触发吐槽入口，把功能藏进场景里。

📱 **极简交互：让 "少操作" 成为安全感**
- **无 "我的" 页面，纯匿名模式**: 不设个人中心，所有记录默认加密存在本地，用指纹 / 密码解锁，彻底断绝 "账号体系" 带来的社交压力；
- **手势操作替代菜单**: 比如左滑删除某条语音，右滑给情绪 "贴标签"，双击树洞图标清空所有记录，用直觉化动作替代文字按钮；
- **"吐完即走" 的轻量化逻辑**: 没有保存 / 发布按钮，录音结束自动存档，甚至不提示 "是否保存"，减少 "思考操作" 的负担，像把心事扔进真实树洞一样干脆。

🎨 **视觉设计：用 "自然元素" 弱化工具感**
- **拟物化树洞容器**: 录音界面像一个木质树洞，语音波形变成树洞里的回声波纹，播放时波纹会震动树叶；
- **动态情绪反馈**: 吐槽完后，树洞周围会生长出对应情绪的植物（比如烦躁时冒出带刺的藤蔓，治愈后藤蔓开出小花），用视觉隐喻替代文字反馈；
- **无边界背景**: 设置 "白噪音 + 自然动画" 背景（比如飘雪、篝火、溪流），操作按钮融入场景（比如 "删除" 是一片飘落的叶子，点击后树叶飞走）。

这样的设计既保留了 "即时倾诉" 的核心需求，又用场景化交互把 "工具感" 藏进自然氛围里，用户打开 APP 就像钻进一个秘密角落，不需要思考 "该点哪里"，本能地就想开口说话～ 

## 4. 技术栈建议 (Tech Stack Proposal)

- **开发框架 (Framework)**: **Flutter**
    - 理由: 非常适合构建具有丰富自定义动画和非标准UI的跨平台应用，与本项目的设计理念高度契合。

- **核心依赖库 (Key Libraries/Plugins for Flutter)**:
    - **音频处理**: `flutter_sound` - 用于录音和播放。
    - **本地存储**: `path_provider` + `encrypt` - 获取文件系统路径并对语音文件进行加密存储。
    - **安全与认证**: `local_auth` - 实现指纹/面容ID解锁。`flutter_secure_storage` - 安全地存储加密密钥。
    - **状态管理**: `riverpod` - 现代、灵活的状态管理方案。
    - **桌面快捷方式**: `quick_actions` - 实现长按App图标的快捷操作。
    - **设备端AI (Advanced)**: `tflite_flutter` - 用于实现"情绪天气"的本地情绪分析模型（V2功能）。

## 5. 开发路线图 (Development Roadmap)

### Phase 1: MVP - 核心功能闭环
1.  **项目搭建**: 创建Flutter项目，引入基础依赖。
2.  **核心功能**: 实现语音的录制、加密存储、解密播放。
3.  **安全门禁**: 集成`local_auth`，实现App的生物识别解锁。
4.  **基础列表**: 创建一个简单的列表，用于展示和管理已录制的语音。

### Phase 2: 沉浸式体验
1.  **UI/UX 优化**:
    - 设计并实现"树洞"主界面和沉浸式录音界面。
    - 将简单列表替换为与场景融合的交互方式。
2.  **手势操作**: 实现左滑删除、右滑标记等核心手势。
3.  **音频体验**:
    - 加入语音波形可视化效果。
    - 集成背景自然音效。

### Phase 3: 高级功能与润色
1.  **高级交互**: 实现长按App图标快捷录音。
2.  **情感化设计**:
    - 实现"情绪天气"或"情绪植物"等视觉反馈。
    - 探索并集成设备端的轻量级情绪分析能力。
3.  **细节打磨**: 完善动画效果、增加触觉反馈、优化性能。

## 6. 交互流程与界面反馈

### 树洞入口页核心交互方案：长按图标即触发，场景化即视感

#### 1. 桌面图标交互（一键唤醒）
- **图标设计**：圆形木质树洞造型，表面有自然纹理，树洞洞口有轻微呼吸光效（类似篝火闪烁），暗示 "这里可以安放情绪"。
- **长按交互**：在手机桌面长按图标，直接弹出半透明录音浮窗（不进入 APP 主界面），浮窗背景是树洞内部的动态阴影，顶部显示 "按住说话" 按钮，手指按下即开始录音，松开结束。
- **快捷反馈**：录音时树洞图标会 "震动"，浮窗内同步生成声波波纹（像树洞里的回声），录完自动保存并提示 "已存入树洞"，整个过程不超过 3 秒，真正实现 "掏手机→吐槽→关屏" 的极简流程。

#### 2. APP 主界面（场景化入口）
- **背景设计**：全屏动态森林场景，中央是一棵参天大树，树干上有一个发光的树洞洞口，周围有飘落的树叶、飞舞的萤火虫（根据时间显示昼夜变化，夜晚有月光）。
- **核心交互**：
    - **点击树洞洞口**：弹出半透明录音面板，面板边缘是树皮纹理，底部有 "录音""播放最近""清空" 三个按钮（按钮设计成树叶、松果、流水等自然元素）。
    - **滑动树干**：树干上会浮现过往情绪标签（如 "生气""难过""开心"），点击标签可快速查看对应历史录音。
    - **摇晃手机**：树洞周围会飘落更多树叶，点击树叶可随机播放一条匿名用户的 "已公开" 治愈语音（可选功能，增加互动感）。
- **动态反馈**：当用户开始录音时，树洞洞口的光会变亮，背景森林音效（风声、鸟鸣）自动降低，突出人声，录完后洞口光效渐变柔和，仿佛心事被 "吸收" 进树里。

#### 3. 录音面板细节（沉浸式体验）
- **界面设计**：半透明毛玻璃质感，底部模拟树洞内壁的凹凸纹理，顶部显示录音时长（用 "滴 —— 答 ——" 的树洞滴水声计时）。
- **操作手势**：
    - **左右滑动面板**：切换 "语音模式" 和 "文字速记模式"（适合不方便说话的场景）。
    - **向上拖拽面板**：展开 "情绪标签" 抽屉（标签像挂在树枝上的纸条，如 "工作压力""情感烦恼"），录音结束后可一键勾选。
    - **捏合屏幕**：触发 "秘密销毁" 功能（类似捏碎纸团的手势），确认后当前录音立即删除，树洞背景会飘落灰烬动画。 