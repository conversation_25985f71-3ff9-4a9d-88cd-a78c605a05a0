{"buildFiles": ["D:\\flutter\\sdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Program Files\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ai-workspace\\echo-cave\\android\\app\\.cxx\\RelWithDebInfo\\15856i2b\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\Program Files\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ai-workspace\\echo-cave\\android\\app\\.cxx\\RelWithDebInfo\\15856i2b\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}