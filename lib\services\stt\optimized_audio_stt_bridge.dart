import 'dart:async';
import 'package:flutter/foundation.dart';
import '../parallel_audio_io_service.dart';
import 'streaming_stt_service.dart';
import 'stt_models.dart';
import 'stt_config_service.dart';
import 'audio_activity_detector.dart';

/// 优化版音频STT桥接服务
/// 
/// 集成音频活动检测，减少不必要的API调用，降低成本
class OptimizedAudioSttBridge extends ChangeNotifier {
  // 服务依赖
  final ParallelAudioIOService _audioService;
  StreamingSttService? _sttService;
  final AudioActivityDetector _activityDetector = AudioActivityDetector();
  
  // 状态管理
  bool _isActive = false;
  bool _isInitialized = false;
  bool _isSttConnected = false;
  StreamSubscription<Uint8List>? _audioSubscription;
  Timer? _silenceTimer;
  
  // 事件流控制器
  final StreamController<SttResult> _resultController = StreamController<SttResult>.broadcast();
  final StreamController<SttError> _errorController = StreamController<SttError>.broadcast();
  
  // 配置
  SttConfig _config = SttConfigService.createDefaultConfig();
  AudioActivityConfig _activityConfig = const AudioActivityConfig();
  
  // 统计信息
  int _totalAudioBytes = 0;
  int _sentAudioBytes = 0;
  int _skippedFrames = 0;
  DateTime? _sessionStartTime;
  DateTime? _lastSttSendTime;

  OptimizedAudioSttBridge(this._audioService);

  /// 当前状态
  bool get isActive => _isActive;
  bool get isInitialized => _isInitialized;
  bool get hasAudioService => _audioService.isInitialized;
  bool get hasSttService => _sttService != null;
  bool get isSttConnected => _isSttConnected;
  bool get isVoiceActive => _activityDetector.isActive;
  
  /// 配置信息
  SttConfig get config => _config;
  AudioActivityConfig get activityConfig => _activityConfig;
  
  /// 成本节省统计
  Map<String, dynamic> get costSavingStats {
    final totalFrames = _totalAudioBytes > 0 ? _totalAudioBytes ~/ 1024 : 0; // 假设每帧1KB
    final sentFrames = _sentAudioBytes > 0 ? _sentAudioBytes ~/ 1024 : 0;
    final savedFrames = totalFrames - sentFrames;
    final savingRatio = totalFrames > 0 ? savedFrames / totalFrames : 0.0;
    
    return {
      'totalAudioBytes': _totalAudioBytes,
      'sentAudioBytes': _sentAudioBytes,
      'savedBytes': _totalAudioBytes - _sentAudioBytes,
      'savingRatio': savingRatio,
      'skippedFrames': _skippedFrames,
      'estimatedCostSaving': '${(savingRatio * 100).toStringAsFixed(1)}%',
      'activityStats': _activityDetector.stats,
    };
  }

  /// 识别结果流
  Stream<SttResult> get onResult => _resultController.stream;
  
  /// 错误事件流
  Stream<SttError> get onError => _errorController.stream;

  /// 初始化桥接服务
  Future<bool> initialize({
    SttConfig? config,
    AudioActivityConfig? activityConfig,
  }) async {
    try {
      if (config != null) {
        _config = config;
      }
      
      if (activityConfig != null) {
        _activityConfig = activityConfig;
      }

      // 确保音频服务已初始化
      if (!_audioService.isInitialized) {
        final audioInitialized = await _audioService.initialize();
        if (!audioInitialized) {
          debugPrint('音频服务初始化失败');
          return false;
        }
      }

      // 创建STT服务
      _sttService = SttConfigService.createDefaultService();
      if (_sttService == null) {
        debugPrint('无法创建STT服务，请检查配置');
        return false;
      }

      // 初始化STT服务
      final sttInitialized = await _sttService!.initialize(_config);
      if (!sttInitialized) {
        debugPrint('STT服务初始化失败');
        return false;
      }

      // 监听STT事件
      _sttService!.onResult.listen(_onSttResult);
      _sttService!.onError.listen(_onSttError);

      _isInitialized = true;
      notifyListeners();
      
      debugPrint('优化版音频STT桥接服务初始化成功');
      debugPrint('活动检测配置: 静音阈值=${_activityConfig.silenceThreshold}, 最大静音=${_activityConfig.maxSilenceDuration}秒');
      return true;

    } catch (e) {
      debugPrint('优化版音频STT桥接服务初始化失败: $e');
      return false;
    }
  }

  /// 开始实时语音识别
  Future<void> startRecognition() async {
    if (!_isInitialized || _isActive) {
      debugPrint('桥接服务未初始化或已在运行中');
      return;
    }

    try {
      // 启动音频活动检测
      _activityDetector.startSession();
      
      // 启动音频流录制
      await _audioService.startStreamRecording();
      
      // 订阅音频流
      _audioSubscription = _audioService.audioStream?.listen(
        _onAudioData,
        onError: _onAudioError,
        onDone: _onAudioDone,
      );

      _isActive = true;
      _sessionStartTime = DateTime.now();
      _totalAudioBytes = 0;
      _sentAudioBytes = 0;
      _skippedFrames = 0;
      _isSttConnected = false;
      
      // 启动静音监控定时器
      _startSilenceMonitoring();
      
      notifyListeners();
      debugPrint('开始优化版实时语音识别');

    } catch (e) {
      debugPrint('启动语音识别失败: $e');
      _emitError(SttErrorType.unknown, 'START_FAILED', '启动识别失败: $e', e);
      rethrow;
    }
  }

  /// 停止实时语音识别
  Future<void> stopRecognition() async {
    if (!_isActive) return;

    try {
      // 停止静音监控
      _silenceTimer?.cancel();
      _silenceTimer = null;
      
      // 停止音频流订阅
      await _audioSubscription?.cancel();
      _audioSubscription = null;

      // 如果STT连接已建立，停止监听
      if (_isSttConnected) {
        await _sttService?.stopListening();
        _isSttConnected = false;
      }

      // 停止音频录制
      await _audioService.stopRecording();
      
      // 结束活动检测
      _activityDetector.endSession();

      _isActive = false;
      notifyListeners();
      
      // 输出成本节省统计
      final stats = costSavingStats;
      debugPrint('停止优化版实时语音识别');
      debugPrint('成本节省统计: ${stats['estimatedCostSaving']} (节省 ${stats['savedBytes']} 字节)');

    } catch (e) {
      debugPrint('停止语音识别失败: $e');
      _emitError(SttErrorType.unknown, 'STOP_FAILED', '停止识别失败: $e', e);
    }
  }

  /// 处理音频数据（带活动检测优化）
  void _onAudioData(Uint8List audioData) {
    if (!_isActive) return;

    _totalAudioBytes += audioData.length;
    
    try {
      // 检测音频活动
      final hasActivity = _activityDetector.detectActivity(audioData);
      
      if (hasActivity) {
        // 检测到语音活动，确保STT连接已建立
        if (!_isSttConnected) {
          _connectStt();
        }
        
        // 发送音频数据到STT服务
        if (_isSttConnected && _sttService != null) {
          _sttService!.sendAudioData(audioData);
          _sentAudioBytes += audioData.length;
          _lastSttSendTime = DateTime.now();
        }
      } else {
        // 没有检测到语音活动，跳过发送
        _skippedFrames++;
        
        // 如果静音时间过长，断开STT连接以节省成本
        if (_isSttConnected && _activityDetector.silenceDuration > _activityConfig.maxSilenceDuration) {
          _disconnectStt();
        }
      }
      
    } catch (e) {
      debugPrint('处理音频数据失败: $e');
      _emitError(SttErrorType.audioFormat, 'AUDIO_PROCESS_FAILED', '音频处理失败: $e', e);
    }
  }

  /// 连接STT服务
  Future<void> _connectStt() async {
    if (_isSttConnected || _sttService == null) return;
    
    try {
      await _sttService!.startListening();
      _isSttConnected = true;
      debugPrint('STT连接已建立（检测到语音活动）');
    } catch (e) {
      debugPrint('建立STT连接失败: $e');
      _emitError(SttErrorType.network, 'STT_CONNECT_FAILED', 'STT连接失败: $e', e);
    }
  }

  /// 断开STT服务
  Future<void> _disconnectStt() async {
    if (!_isSttConnected || _sttService == null) return;
    
    try {
      await _sttService!.stopListening();
      _isSttConnected = false;
      debugPrint('STT连接已断开（长时间静音）');
    } catch (e) {
      debugPrint('断开STT连接失败: $e');
    }
  }

  /// 启动静音监控
  void _startSilenceMonitoring() {
    _silenceTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isActive) {
        timer.cancel();
        return;
      }
      
      // 检查是否需要因长时间静音而断开STT
      if (_isSttConnected && _activityDetector.silenceDuration > _activityConfig.maxSilenceDuration) {
        _disconnectStt();
      }
    });
  }

  /// 处理音频错误
  void _onAudioError(error) {
    debugPrint('音频流错误: $error');
    _emitError(SttErrorType.audioFormat, 'AUDIO_STREAM_ERROR', '音频流错误: $error', error);
  }

  /// 音频流结束
  void _onAudioDone() {
    debugPrint('音频流结束');
    stopRecognition();
  }

  /// 处理STT识别结果
  void _onSttResult(SttResult result) {
    _resultController.add(result);
    debugPrint('STT识别结果: ${result.text} (最终: ${result.isFinal})');
  }

  /// 处理STT错误
  void _onSttError(SttError error) {
    _errorController.add(error);
    debugPrint('STT错误: ${error.message}');
  }

  /// 发送错误事件
  void _emitError(SttErrorType type, String code, String message, [Object? originalError]) {
    final error = SttError(
      code: code,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      originalError: originalError,
    );
    
    _errorController.add(error);
  }

  /// 获取详细统计信息
  Map<String, dynamic> getDetailedStats() {
    final sessionDuration = _sessionStartTime != null
        ? DateTime.now().difference(_sessionStartTime!).inMilliseconds / 1000.0
        : 0.0;
    
    return {
      ...costSavingStats,
      'sessionDuration': sessionDuration,
      'isSttConnected': _isSttConnected,
      'isVoiceActive': _activityDetector.isActive,
      'lastSttSendTime': _lastSttSendTime?.toString(),
      'activityDetectorDebug': _activityDetector.getDebugInfo(),
    };
  }

  /// 更新活动检测配置
  void updateActivityConfig(AudioActivityConfig newConfig) {
    _activityConfig = newConfig;
    debugPrint('活动检测配置已更新');
    notifyListeners();
  }

  @override
  void dispose() {
    stopRecognition();
    _resultController.close();
    _errorController.close();
    _sttService?.dispose();
    super.dispose();
  }
}
