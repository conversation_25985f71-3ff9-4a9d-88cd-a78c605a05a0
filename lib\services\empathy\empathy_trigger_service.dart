import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'emotion_models.dart';

/// 共鸣触发服务
class EmpathyTriggerService extends ChangeNotifier {
  // 触发配置
  static const double _emotionThreshold = 0.5;
  static const double _speechRateThresholdHigh = 8.0;
  static const double _speechRateThresholdLow = 3.0;
  static const double _longPauseThreshold = 2.0;
  static const int _repetitionWindowSize = 5;
  static const double _repetitionThreshold = 0.7;
  
  // 状态管理
  final List<EmpathyTrigger> _triggerHistory = [];
  final List<EmotionAnalysis> _emotionBuffer = [];
  final List<SpeechFeatures> _speechBuffer = [];
  
  // 事件流
  final StreamController<EmpathyTrigger> _triggerController = 
      StreamController<EmpathyTrigger>.broadcast();
  
  // 触发抑制（避免过于频繁的触发）
  DateTime? _lastTriggerTime;
  static const Duration _triggerCooldown = Duration(seconds: 10);
  
  // 会话状态
  bool _isSessionActive = false;
  DateTime? _sessionStartTime;

  /// 触发事件流
  Stream<EmpathyTrigger> get onTrigger => _triggerController.stream;
  
  /// 触发历史
  List<EmpathyTrigger> get triggerHistory => List.unmodifiable(_triggerHistory);
  
  /// 是否在冷却期
  bool get isInCooldown {
    if (_lastTriggerTime == null) return false;
    return DateTime.now().difference(_lastTriggerTime!) < _triggerCooldown;
  }
  
  /// 会话是否活跃
  bool get isSessionActive => _isSessionActive;

  /// 开始新的触发会话
  void startSession() {
    _isSessionActive = true;
    _sessionStartTime = DateTime.now();
    _triggerHistory.clear();
    _emotionBuffer.clear();
    _speechBuffer.clear();
    _lastTriggerTime = null;
    
    debugPrint('共鸣触发会话开始');
    notifyListeners();
  }

  /// 结束触发会话
  void endSession() {
    _isSessionActive = false;
    _sessionStartTime = null;
    
    debugPrint('共鸣触发会话结束');
    notifyListeners();
  }

  /// 分析情感并检查触发条件
  Future<List<EmpathyTrigger>> analyzeAndTrigger({
    EmotionAnalysis? emotionAnalysis,
    SpeechFeatures? speechFeatures,
  }) async {
    if (!_isSessionActive) return [];
    
    final triggers = <EmpathyTrigger>[];
    final now = DateTime.now();
    
    // 更新缓冲区
    if (emotionAnalysis != null) {
      _emotionBuffer.add(emotionAnalysis);
      if (_emotionBuffer.length > 20) _emotionBuffer.removeAt(0);
    }
    
    if (speechFeatures != null) {
      _speechBuffer.add(speechFeatures);
      if (_speechBuffer.length > 20) _speechBuffer.removeAt(0);
    }
    
    // 检查各种触发条件
    if (emotionAnalysis != null) {
      triggers.addAll(_checkEmotionTriggers(emotionAnalysis, now));
    }
    
    if (speechFeatures != null) {
      triggers.addAll(_checkSpeechTriggers(speechFeatures, now));
    }
    
    triggers.addAll(_checkPatternTriggers(now));
    
    // 过滤和排序触发器
    final filteredTriggers = _filterTriggers(triggers);
    
    // 记录触发器并发送事件
    for (final trigger in filteredTriggers) {
      _triggerHistory.add(trigger);
      _triggerController.add(trigger);
      
      if (trigger.shouldTriggerImmediately) {
        _lastTriggerTime = now;
      }
      
      debugPrint('触发共鸣: $trigger');
    }
    
    // 限制历史记录长度
    if (_triggerHistory.length > 100) {
      _triggerHistory.removeAt(0);
    }
    
    notifyListeners();
    return filteredTriggers;
  }

  /// 检查情感相关的触发条件
  List<EmpathyTrigger> _checkEmotionTriggers(EmotionAnalysis emotion, DateTime timestamp) {
    final triggers = <EmpathyTrigger>[];
    
    // 1. 强烈负面情感触发
    if (emotion.isNegative && emotion.intensity == EmotionIntensity.high) {
      triggers.add(EmpathyTrigger(
        type: EmpathyTriggerType.emotionIntensity,
        strength: min(1.0, emotion.confidence + 0.2),
        reason: '检测到强烈的${_getEmotionName(emotion.primaryEmotion)}情感',
        timestamp: timestamp,
        suggestedResponse: _getSuggestedResponse(emotion.primaryEmotion),
        emotionAnalysis: emotion,
      ));
    }
    
    // 2. 情感关键词触发
    if (emotion.keywords.isNotEmpty && emotion.confidence > 0.6) {
      final keywordStrength = min(1.0, emotion.keywords.length * 0.3);
      triggers.add(EmpathyTrigger(
        type: EmpathyTriggerType.emotionalKeyword,
        strength: keywordStrength,
        reason: '检测到情感关键词: ${emotion.keywords.join(", ")}',
        timestamp: timestamp,
        suggestedResponse: EmpathyResponseType.understanding,
        emotionAnalysis: emotion,
      ));
    }
    
    // 3. 求助信号触发
    if (_containsHelpSignal(emotion.keywords)) {
      triggers.add(EmpathyTrigger(
        type: EmpathyTriggerType.helpSignal,
        strength: 0.9,
        reason: '检测到求助信号',
        timestamp: timestamp,
        suggestedResponse: EmpathyResponseType.emotionalSupport,
        emotionAnalysis: emotion,
      ));
    }
    
    // 4. 情感转换触发
    if (_emotionBuffer.length >= 2) {
      final previousEmotion = _emotionBuffer[_emotionBuffer.length - 2];
      if (_hasSignificantEmotionChange(previousEmotion, emotion)) {
        triggers.add(EmpathyTrigger(
          type: EmpathyTriggerType.emotionTransition,
          strength: 0.6,
          reason: '情感从${_getEmotionName(previousEmotion.primaryEmotion)}转为${_getEmotionName(emotion.primaryEmotion)}',
          timestamp: timestamp,
          suggestedResponse: EmpathyResponseType.gentleInquiry,
          emotionAnalysis: emotion,
        ));
      }
    }
    
    return triggers;
  }

  /// 检查语音相关的触发条件
  List<EmpathyTrigger> _checkSpeechTriggers(SpeechFeatures speech, DateTime timestamp) {
    final triggers = <EmpathyTrigger>[];
    
    // 1. 语速异常触发
    if (speech.isSpeakingFast) {
      triggers.add(EmpathyTrigger(
        type: EmpathyTriggerType.speechRateAnomaly,
        strength: 0.7,
        reason: '语速过快，可能表示焦虑或紧张',
        timestamp: timestamp,
        suggestedResponse: EmpathyResponseType.companionship,
        speechFeatures: speech,
      ));
    } else if (speech.isSpeakingSlow) {
      triggers.add(EmpathyTrigger(
        type: EmpathyTriggerType.speechRateAnomaly,
        strength: 0.6,
        reason: '语速过慢，可能表示悲伤或疲惫',
        timestamp: timestamp,
        suggestedResponse: EmpathyResponseType.gentleInquiry,
        speechFeatures: speech,
      ));
    }
    
    // 2. 长时间停顿触发
    if (speech.hasLongPause) {
      triggers.add(EmpathyTrigger(
        type: EmpathyTriggerType.longPause,
        strength: min(1.0, speech.pauseDuration / 5.0),
        reason: '检测到${speech.pauseDuration.toStringAsFixed(1)}秒的停顿',
        timestamp: timestamp,
        suggestedResponse: EmpathyResponseType.gentleInquiry,
        speechFeatures: speech,
      ));
    }
    
    return triggers;
  }

  /// 检查模式相关的触发条件
  List<EmpathyTrigger> _checkPatternTriggers(DateTime timestamp) {
    final triggers = <EmpathyTrigger>[];
    
    // 1. 重复表达触发
    if (_hasRepetitiveExpression()) {
      triggers.add(EmpathyTrigger(
        type: EmpathyTriggerType.repetitiveExpression,
        strength: 0.8,
        reason: '检测到重复的情感表达',
        timestamp: timestamp,
        suggestedResponse: EmpathyResponseType.understanding,
      ));
    }
    
    return triggers;
  }

  /// 过滤触发器（去重、冷却期检查等）
  List<EmpathyTrigger> _filterTriggers(List<EmpathyTrigger> triggers) {
    if (triggers.isEmpty) return triggers;
    
    // 按强度排序
    triggers.sort((a, b) => b.strength.compareTo(a.strength));
    
    // 如果在冷却期，只保留最强的触发器
    if (isInCooldown) {
      return triggers.take(1).where((t) => t.strength > 0.8).toList();
    }
    
    // 去除重复类型的触发器，保留最强的
    final uniqueTriggers = <EmpathyTriggerType, EmpathyTrigger>{};
    for (final trigger in triggers) {
      if (!uniqueTriggers.containsKey(trigger.type) ||
          uniqueTriggers[trigger.type]!.strength < trigger.strength) {
        uniqueTriggers[trigger.type] = trigger;
      }
    }
    
    return uniqueTriggers.values.toList();
  }

  /// 检查是否包含求助信号
  bool _containsHelpSignal(List<String> keywords) {
    const helpSignals = ['帮助', '救救我', '怎么办', '求助', '帮帮我', '没办法'];
    return keywords.any((keyword) => helpSignals.contains(keyword));
  }

  /// 检查是否有显著的情感变化
  bool _hasSignificantEmotionChange(EmotionAnalysis previous, EmotionAnalysis current) {
    // 情感类型变化
    if (previous.primaryEmotion != current.primaryEmotion) {
      // 从积极到消极，或从消极到积极
      if ((previous.isPositive && current.isNegative) ||
          (previous.isNegative && current.isPositive)) {
        return true;
      }
    }
    
    // 情感强度显著变化
    final scoreDiff = (current.emotionScore - previous.emotionScore).abs();
    return scoreDiff > 0.4;
  }

  /// 检查是否有重复表达
  bool _hasRepetitiveExpression() {
    if (_emotionBuffer.length < _repetitionWindowSize) return false;
    
    final recentEmotions = _emotionBuffer
        .skip(_emotionBuffer.length - _repetitionWindowSize)
        .toList();
    
    // 检查情感类型的重复
    final emotionCounts = <EmotionType, int>{};
    for (final emotion in recentEmotions) {
      emotionCounts[emotion.primaryEmotion] = 
          (emotionCounts[emotion.primaryEmotion] ?? 0) + 1;
    }
    
    final maxCount = emotionCounts.values.reduce(max);
    return maxCount / _repetitionWindowSize >= _repetitionThreshold;
  }

  /// 获取情感名称
  String _getEmotionName(EmotionType emotion) {
    const names = {
      EmotionType.sad: '悲伤',
      EmotionType.anxious: '焦虑',
      EmotionType.angry: '愤怒',
      EmotionType.lonely: '孤独',
      EmotionType.stressed: '压力',
      EmotionType.confused: '困惑',
      EmotionType.happy: '快乐',
      EmotionType.hopeful: '希望',
      EmotionType.fearful: '恐惧',
      EmotionType.neutral: '平静',
      EmotionType.positive: '积极',
      EmotionType.negative: '消极',
    };
    return names[emotion] ?? emotion.name;
  }

  /// 获取建议的回应类型
  EmpathyResponseType _getSuggestedResponse(EmotionType emotion) {
    const responses = {
      EmotionType.sad: EmpathyResponseType.emotionalSupport,
      EmotionType.anxious: EmpathyResponseType.companionship,
      EmotionType.angry: EmpathyResponseType.understanding,
      EmotionType.lonely: EmpathyResponseType.companionship,
      EmotionType.stressed: EmpathyResponseType.encouragement,
      EmotionType.confused: EmpathyResponseType.gentleInquiry,
      EmotionType.fearful: EmpathyResponseType.emotionalSupport,
    };
    return responses[emotion] ?? EmpathyResponseType.understanding;
  }

  /// 获取触发统计信息
  Map<String, dynamic> getTriggerStats() {
    if (_triggerHistory.isEmpty) {
      return {
        'totalTriggers': 0,
        'triggerRate': 0.0,
        'mostCommonType': null,
        'averageStrength': 0.0,
      };
    }
    
    final typeCounts = <EmpathyTriggerType, int>{};
    double totalStrength = 0.0;
    
    for (final trigger in _triggerHistory) {
      typeCounts[trigger.type] = (typeCounts[trigger.type] ?? 0) + 1;
      totalStrength += trigger.strength;
    }
    
    final mostCommonType = typeCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
    
    final sessionDuration = _sessionStartTime != null
        ? DateTime.now().difference(_sessionStartTime!).inMinutes
        : 1;
    
    return {
      'totalTriggers': _triggerHistory.length,
      'triggerRate': _triggerHistory.length / sessionDuration,
      'mostCommonType': mostCommonType.name,
      'averageStrength': totalStrength / _triggerHistory.length,
    };
  }

  /// 清除历史记录
  void clearHistory() {
    _triggerHistory.clear();
    _emotionBuffer.clear();
    _speechBuffer.clear();
    _lastTriggerTime = null;
    notifyListeners();
    debugPrint('共鸣触发历史已清除');
  }

  @override
  void dispose() {
    _triggerController.close();
    super.dispose();
  }
}
