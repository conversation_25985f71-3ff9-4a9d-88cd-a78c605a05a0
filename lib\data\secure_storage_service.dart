import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'dart:math';
import 'dart:convert';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final secureStorageServiceProvider = Provider<SecureStorageService>((ref) {
  return SecureStorageService(const FlutterSecureStorage());
});

class SecureStorageService {
  final FlutterSecureStorage _secureStorage;

  SecureStorageService(this._secureStorage);

  static const _keyAlias = 'audio_encryption_key';

  // Public method to get the key
  Future<String?> getEncryptionKey() async {
    return _getOrCreateEncryptionKey();
  }

  Future<String> _getOrCreateEncryptionKey() async {
    String? key = await _secureStorage.read(key: _keyAlias);

    if (key == null) {
      final random = Random.secure();
      final keyBytes = List<int>.generate(32, (_) => random.nextInt(256));
      key = base64Url.encode(keyBytes);
      await _secureStorage.write(key: _keyAlias, value: key);
    }
    return key;
  }

  Future<encrypt.Encrypter> _getEncrypter() async {
    final keyString = await _getOrCreateEncryptionKey();
    final key = encrypt.Key.fromBase64(keyString);
    return encrypt.Encrypter(encrypt.AES(key));
  }

  Future<String> encryptData(String plainText) async {
    final encrypter = await _getEncrypter();
    final iv = encrypt.IV.fromLength(16); // AES block size
    final encrypted = encrypter.encrypt(plainText, iv: iv);
    // Prepend IV for decryption
    return '${iv.base64}:${encrypted.base64}';
  }

  Future<String> decryptData(String encryptedText) async {
    final parts = encryptedText.split(':');
    final iv = encrypt.IV.fromBase64(parts[0]);
    final encrypted = encrypt.Encrypted.fromBase64(parts[1]);

    final encrypter = await _getEncrypter();
    return encrypter.decrypt(encrypted, iv: iv);
  }
}
