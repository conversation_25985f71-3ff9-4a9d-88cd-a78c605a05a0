{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Program Files/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Program Files/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-b4d7bc4960c12ada1461.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-77ba1a0be57cab5b0e42.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-a44e2e85ff08d4d674f5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-77ba1a0be57cab5b0e42.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-a44e2e85ff08d4d674f5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-b4d7bc4960c12ada1461.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}