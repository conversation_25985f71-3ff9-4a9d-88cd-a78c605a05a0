import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/parallel_audio_io_service.dart';
import '../services/stt/audio_stt_bridge.dart';
import '../services/tts_service.dart';
import '../services/empathy/empathy_engine.dart';
import '../services/empathy/emotion_models.dart';
import '../services/ambient_sound_service.dart';
import '../views/main_view.dart';
import '../widgets/emotion_visualization.dart';
import '../widgets/voice_waveform.dart';
import '../widgets/immersive_effects.dart';

/// 沉浸式持续倾听界面
class ImmersiveListeningView extends HookConsumerWidget {
  const ImmersiveListeningView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 服务实例
    final audioService = useMemoized(() => ParallelAudioIOService());
    final sttBridge = useMemoized(() => AudioSttBridge(audioService));
    final ttsService = useMemoized(() => TTSService());
    final empathyEngine = useMemoized(() => EmpathyEngine(
      sttBridge: sttBridge,
      ttsService: ttsService,
    ));
    final ambientService = ref.watch(ambientSoundServiceProvider);
    
    // 状态管理
    final isInitialized = useState(false);
    final isListening = useState(false);
    final currentEmotion = useState<EmotionAnalysis?>(null);
    final recentTriggers = useState<List<EmpathyTrigger>>([]);
    final aiResponses = useState<List<String>>([]);
    final sessionDuration = useState(0);
    final showControls = useState(true);
    
    // 动画控制
    final fadeController = useAnimationController(
      duration: const Duration(milliseconds: 500),
    );

    // 初始化
    useEffect(() {
      _initializeServices(empathyEngine, isInitialized);
      
      // 监听情感分析
      final emotionSubscription = empathyEngine.onEmotionAnalysis.listen((analysis) {
        currentEmotion.value = analysis;
      });
      
      // 监听触发事件
      final triggerSubscription = empathyEngine.onTrigger.listen((trigger) {
        final triggers = List<EmpathyTrigger>.from(recentTriggers.value);
        triggers.add(trigger);
        // 只保留最近10个触发
        if (triggers.length > 10) {
          triggers.removeAt(0);
        }
        recentTriggers.value = triggers;
      });
      
      // 监听AI回应
      final responseSubscription = empathyEngine.onResponse.listen((response) {
        final responses = List<String>.from(aiResponses.value);
        responses.add(response);
        // 只保留最近5个回应
        if (responses.length > 5) {
          responses.removeAt(0);
        }
        aiResponses.value = responses;
      });

      return () {
        emotionSubscription.cancel();
        triggerSubscription.cancel();
        responseSubscription.cancel();
        empathyEngine.dispose();
      };
    }, []);

    // 会话时长计时器
    useEffect(() {
      Timer? timer;
      if (isListening.value) {
        timer = Timer.periodic(const Duration(seconds: 1), (_) {
          sessionDuration.value++;
        });
      }
      return () => timer?.cancel();
    }, [isListening.value]);

    // 控制栏自动隐藏
    useEffect(() {
      Timer? hideTimer;
      if (isListening.value && showControls.value) {
        hideTimer = Timer(const Duration(seconds: 5), () {
          showControls.value = false;
          fadeController.reverse();
        });
      }
      return () => hideTimer?.cancel();
    }, [isListening.value, showControls.value]);

    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: () {
          // 点击屏幕显示/隐藏控制栏
          showControls.value = !showControls.value;
          if (showControls.value) {
            fadeController.forward();
          } else {
            fadeController.reverse();
          }
        },
        child: Stack(
          children: [
            // 背景渐变
            _buildBackground(currentEmotion.value),

            // 粒子效果
            if (isListening.value)
              Positioned.fill(
                child: ParticleEffect(
                  isActive: isListening.value,
                  color: _getEmotionColor(currentEmotion.value?.primaryEmotion)
                      .withValues(alpha: 0.3),
                  particleCount: 30,
                ),
              ),

            // 主要内容区域
            _buildMainContent(
              currentEmotion.value,
              recentTriggers.value,
              isListening.value,
              aiResponses.value,
            ),
            
            // 顶部状态栏
            _buildTopStatusBar(
              context,
              isListening.value,
              sessionDuration.value,
              fadeController,
            ),
            
            // 底部控制栏
            _buildBottomControls(
              context,
              empathyEngine,
              isInitialized.value,
              isListening,
              fadeController,
              ambientService,
            ),
            
            // AI回应显示
            if (aiResponses.value.isNotEmpty)
              _buildAiResponseOverlay(aiResponses.value.last),
          ],
        ),
      ),
    );
  }

  /// 初始化服务
  Future<void> _initializeServices(
    EmpathyEngine empathyEngine,
    ValueNotifier<bool> isInitialized,
  ) async {
    try {
      final success = await empathyEngine.initialize();
      isInitialized.value = success;
    } catch (e) {
      debugPrint('初始化沉浸式界面失败: $e');
    }
  }

  /// 构建背景
  Widget _buildBackground(EmotionAnalysis? emotion) {
    Color primaryColor = const Color(0xFF0F0F23);
    Color secondaryColor = const Color(0xFF1A1A2E);
    
    if (emotion != null) {
      switch (emotion.primaryEmotion) {
        case EmotionType.sad:
          primaryColor = const Color(0xFF1A237E);
          secondaryColor = const Color(0xFF3F51B5);
          break;
        case EmotionType.anxious:
          primaryColor = const Color(0xFFE65100);
          secondaryColor = const Color(0xFFFF9800);
          break;
        case EmotionType.angry:
          primaryColor = const Color(0xFFB71C1C);
          secondaryColor = const Color(0xFFD32F2F);
          break;
        case EmotionType.happy:
          primaryColor = const Color(0xFF1B5E20);
          secondaryColor = const Color(0xFF4CAF50);
          break;
        case EmotionType.hopeful:
          primaryColor = const Color(0xFF004D40);
          secondaryColor = const Color(0xFF009688);
          break;
        default:
          break;
      }
    }
    
    return AnimatedContainer(
      duration: const Duration(seconds: 2),
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 1.5,
          colors: [
            primaryColor.withValues(alpha: 0.3),
            secondaryColor.withValues(alpha: 0.1),
            Colors.black,
          ],
        ),
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent(
    EmotionAnalysis? emotion,
    List<EmpathyTrigger> triggers,
    bool isListening,
    List<String> responses,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 情感可视化核心（带呼吸光效）
          BreathingGlow(
            isActive: isListening,
            glowColor: _getEmotionColor(emotion?.primaryEmotion),
            intensity: emotion?.confidence ?? 0.5,
            child: EmotionVisualization(
              currentEmotion: emotion,
              recentTriggers: triggers,
              isListening: isListening,
              size: 280.0,
            ),
          ),
          
          const SizedBox(height: 40),
          
          // 语音波形
          VoiceWaveform(
            isRecording: isListening,
            amplitude: emotion?.confidence ?? 0.5,
            color: _getEmotionColor(emotion?.primaryEmotion),
            height: 80.0,
            barCount: 25,
          ),
          
          const SizedBox(height: 30),
          
          // 情感状态文字
          if (emotion != null)
            _buildEmotionText(emotion),
        ],
      ),
    );
  }

  /// 构建情感状态文字
  Widget _buildEmotionText(EmotionAnalysis emotion) {
    return Column(
      children: [
        Text(
          _getEmotionName(emotion.primaryEmotion),
          style: TextStyle(
            color: _getEmotionColor(emotion.primaryEmotion),
            fontSize: 24,
            fontWeight: FontWeight.w300,
            letterSpacing: 2.0,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${_getIntensityName(emotion.intensity)} | ${(emotion.confidence * 100).toStringAsFixed(0)}%',
          style: const TextStyle(
            color: Colors.white54,
            fontSize: 14,
            letterSpacing: 1.0,
          ),
        ),
      ],
    );
  }

  /// 构建顶部状态栏
  Widget _buildTopStatusBar(
    BuildContext context,
    bool isListening,
    int duration,
    AnimationController fadeController,
  ) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      left: 16,
      right: 16,
      child: FadeTransition(
        opacity: fadeController,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 返回按钮
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back, color: Colors.white54),
            ),
            
            // 会话状态
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  VoiceActivityIndicator(
                    isActive: isListening,
                    color: Colors.tealAccent,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isListening ? _formatDuration(duration) : '未开始',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            
            // 占位符
            const SizedBox(width: 48),
          ],
        ),
      ),
    );
  }

  /// 构建底部控制栏
  Widget _buildBottomControls(
    BuildContext context,
    EmpathyEngine empathyEngine,
    bool isInitialized,
    ValueNotifier<bool> isListening,
    AnimationController fadeController,
    AmbientSoundService ambientService,
  ) {
    return Positioned(
      bottom: MediaQuery.of(context).padding.bottom + 32,
      left: 32,
      right: 32,
      child: FadeTransition(
        opacity: fadeController,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // 环境音效按钮
            _buildControlButton(
              icon: Icons.music_note,
              onPressed: () => _toggleAmbientSound(ambientService),
              color: Colors.purple,
            ),
            
            // 主要控制按钮
            _buildMainControlButton(
              empathyEngine: empathyEngine,
              isInitialized: isInitialized,
              isListening: isListening,
            ),
            
            // 设置按钮
            _buildControlButton(
              icon: Icons.settings,
              onPressed: () => _showSettings(context),
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建主要控制按钮
  Widget _buildMainControlButton({
    required EmpathyEngine empathyEngine,
    required bool isInitialized,
    required ValueNotifier<bool> isListening,
  }) {
    return GestureDetector(
      onTap: isInitialized
          ? () => _toggleListening(empathyEngine, isListening)
          : null,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isListening.value ? Colors.red : Colors.tealAccent,
          boxShadow: [
            BoxShadow(
              color: (isListening.value ? Colors.red : Colors.tealAccent)
                  .withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Icon(
          isListening.value ? Icons.stop : Icons.mic,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color.withValues(alpha: 0.2),
          border: Border.all(color: color.withValues(alpha: 0.5), width: 1),
        ),
        child: Icon(
          icon,
          color: color,
          size: 24,
        ),
      ),
    );
  }

  /// 构建AI回应覆盖层
  Widget _buildAiResponseOverlay(String response) {
    return Positioned(
      top: 120,
      left: 32,
      right: 32,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.tealAccent.withValues(alpha: 0.3)),
        ),
        child: Text(
          response,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// 切换监听状态
  Future<void> _toggleListening(
    EmpathyEngine empathyEngine,
    ValueNotifier<bool> isListening,
  ) async {
    try {
      if (isListening.value) {
        await empathyEngine.stop();
        isListening.value = false;
      } else {
        await empathyEngine.start();
        isListening.value = true;
        // 触觉反馈
        HapticUtils.medium();
      }
    } catch (e) {
      debugPrint('切换监听状态失败: $e');
    }
  }

  /// 切换环境音效
  void _toggleAmbientSound(AmbientSoundService ambientService) {
    if (ambientService.isPlaying) {
      ambientService.stop();
    } else {
      ambientService.setTime(false); // 播放夜间环境音
    }
    HapticUtils.selection();
  }

  /// 显示设置
  void _showSettings(BuildContext context) {
    // TODO: 实现设置界面
    HapticUtils.selection();
  }

  /// 获取情感颜色
  Color _getEmotionColor(EmotionType? emotion) {
    if (emotion == null) return Colors.tealAccent;
    
    const colors = {
      EmotionType.sad: Colors.blue,
      EmotionType.anxious: Colors.orange,
      EmotionType.angry: Colors.red,
      EmotionType.lonely: Colors.purple,
      EmotionType.stressed: Colors.deepOrange,
      EmotionType.confused: Colors.amber,
      EmotionType.happy: Colors.green,
      EmotionType.hopeful: Colors.lightGreen,
      EmotionType.fearful: Colors.indigo,
      EmotionType.neutral: Colors.grey,
    };
    
    return colors[emotion] ?? Colors.tealAccent;
  }

  /// 获取情感名称
  String _getEmotionName(EmotionType emotion) {
    const names = {
      EmotionType.sad: '悲伤',
      EmotionType.anxious: '焦虑',
      EmotionType.angry: '愤怒',
      EmotionType.lonely: '孤独',
      EmotionType.stressed: '压力',
      EmotionType.confused: '困惑',
      EmotionType.happy: '快乐',
      EmotionType.hopeful: '希望',
      EmotionType.fearful: '恐惧',
      EmotionType.neutral: '平静',
    };
    return names[emotion] ?? '未知';
  }

  /// 获取强度名称
  String _getIntensityName(EmotionIntensity intensity) {
    const names = {
      EmotionIntensity.low: '轻微',
      EmotionIntensity.medium: '中等',
      EmotionIntensity.high: '强烈',
      EmotionIntensity.extreme: '极强',
    };
    return names[intensity] ?? '未知';
  }

  /// 格式化时长
  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
