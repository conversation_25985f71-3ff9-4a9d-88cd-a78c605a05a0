# AEC回声消除效果验证指南

## 概述

本文档提供了验证Echo Cave应用中AEC（声学回声消除）功能效果的详细指南。通过对比测试，我们可以评估启用AEC前后的录音质量差异。

## 验证环境要求

### 硬件要求
- **iOS设备**：iPhone 6s或更新版本（支持enableVoiceProcessing）
- **Android设备**：用于对比测试（不支持flutter_sound的AEC功能）
- **测试环境**：安静的室内环境，避免外部噪音干扰

### 软件要求
- Echo Cave应用（包含AEC功能）
- 测试音频文件（项目中的day_ambient.mp3或night_ambient.mp3）

## 验证步骤

### 第一步：准备测试环境

1. **打开Echo Cave应用**
2. **进入AEC测试界面**：
   - 点击主界面的树洞图标
   - 选择"🔊 AEC测试"选项
3. **确认服务状态**：
   - 检查"录音状态"和"播放状态"显示为"就绪"
   - 确认平台信息正确显示

### 第二步：运行AEC对比测试

1. **启动对比测试**：
   - 点击"AEC效果对比测试"按钮（绿色按钮）
   - 系统将自动进行两次连续测试

2. **测试流程**：
   - **第一次测试**：标准录音（无AEC）
     - 持续时间：8秒
     - 同时播放背景音频和录制用户语音
   - **等待间隔**：2秒
   - **第二次测试**：AEC录音（启用AEC）
     - 持续时间：8秒
     - 启用enableVoiceProcessing功能

### 第三步：音频质量评估

#### 主观听觉评估标准

**标准录音（无AEC）预期效果**：
- ✅ 能清晰听到用户说话声音
- ❌ 能明显听到背景播放的音频（回声）
- ❌ 可能存在音频重叠和干扰

**AEC录音预期效果**：
- ✅ 能清晰听到用户说话声音
- ✅ 背景播放音频被显著抑制或完全消除
- ✅ 录音质量清晰，无明显回声干扰

#### 评估方法

1. **播放录音文件**：
   - 在测试结果列表中找到两个录音文件
   - 分别播放并仔细聆听
   - 注意背景音频的存在程度

2. **对比分析**：
   - 比较两个录音中背景音频的音量
   - 评估用户语音的清晰度
   - 记录回声消除的效果

## 技术实现细节

### iOS AEC功能
```dart
// 启用AEC的录音配置
await _recorder!.startRecorder(
  toFile: _currentRecordingPath,
  codec: fs.Codec.aacMP4,
  audioSource: fs.AudioSource.microphone,
  enableVoiceProcessing: true, // 关键：启用语音处理
);
```

### 测试模式说明

1. **分离模式**：基础功能验证，先录音后播放
2. **同时模式**：测试引擎冲突，同时录音播放
3. **iOS AEC模式**：使用flutter_sound的enableVoiceProcessing
4. **混合引擎**：FlutterSound录音 + JustAudio播放
5. **流式处理**：实时音频数据流处理

## 预期结果

### 成功标准
- **iOS设备**：AEC录音中背景音频应被显著抑制（减少80%以上）
- **Android设备**：由于不支持AEC，两次录音效果应该相似
- **录音质量**：用户语音在两种模式下都应保持清晰

### 失败情况
- AEC录音中仍能清晰听到背景音频
- 用户语音质量在AEC模式下明显下降
- 录音过程中出现异常错误

## 故障排除

### 常见问题

1. **权限问题**：
   - 确保应用已获得麦克风权限
   - 检查iOS设备的隐私设置

2. **音频冲突**：
   - 关闭其他音频应用
   - 确保设备音量适中

3. **测试失败**：
   - 重启应用重试
   - 检查设备存储空间
   - 查看错误日志

### 调试信息
- 测试结果会显示详细的状态信息
- 可以生成测试报告查看完整日志
- 录音文件保存在应用文档目录中

## 结论

通过本验证指南，开发团队可以客观评估AEC功能的实际效果，为后续的实时AI语音陪伴功能提供技术保障。成功的AEC验证将确保AI播放回复时不会干扰用户的语音输入，实现真正的并行音频处理。
