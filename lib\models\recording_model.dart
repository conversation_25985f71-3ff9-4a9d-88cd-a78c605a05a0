import 'package:flutter/foundation.dart';

enum RecordingType { PURE_RECORDING, AI_CONVERSATION }

@immutable
class Recording {
  final int id;
  final String filePath;
  final String title;
  final int timestamp;
  final List<String> tags;
  final bool isTagging;
  final RecordingType type;
  final String? conversationId;

  const Recording({
    required this.id,
    required this.filePath,
    this.title = '',
    required this.timestamp,
    this.tags = const [],
    this.isTagging = false,
    this.type = RecordingType.PURE_RECORDING,
    this.conversationId,
  });

  Recording copyWith({
    int? id,
    String? filePath,
    String? title,
    int? timestamp,
    List<String>? tags,
    bool? isTagging,
    RecordingType? type,
    String? conversationId,
  }) {
    return Recording(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      title: title ?? this.title,
      timestamp: timestamp ?? this.timestamp,
      tags: tags ?? this.tags,
      isTagging: isTagging ?? this.isTagging,
      type: type ?? this.type,
      conversationId: conversationId ?? this.conversationId,
    );
  }
}
