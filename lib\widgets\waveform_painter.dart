import 'dart:math' as math;
import 'package:flutter/material.dart';

class WaveformPainter extends CustomPainter {
  final double dbLevel;
  WaveformPainter({required this.dbLevel});

  @override
  void paint(Canvas canvas, Size size) {
    if (dbLevel <= 0) return;

    final center = Offset(size.width / 2, size.height / 2);
    // Normalize dB to 0-1 range (120 is max for flutter_sound)
    final normalizedDb = math.min(dbLevel.abs() / 120, 1.0);

    final maxRadius = size.width / 2 * 0.8;
    const waveCount = 3;

    for (int i = 0; i < waveCount; i++) {
      final progress = (i + (1 - normalizedDb)) / waveCount;
      final radius = maxRadius * progress;
      final alpha = (255 * (1.0 - progress) * 0.5).toInt();

      final paint = Paint()
        ..color = Colors.white.withAlpha(alpha)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true; // Always repaint to show animation
  }
}
