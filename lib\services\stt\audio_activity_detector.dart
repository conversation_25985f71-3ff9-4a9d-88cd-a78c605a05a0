import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

/// 音频活动检测器
/// 
/// 用于检测音频中的语音活动，避免在静音期间发送不必要的API请求
class AudioActivityDetector {
  // 配置参数
  static const double _silenceThreshold = 0.01; // 静音阈值
  static const int _silenceFramesThreshold = 10; // 连续静音帧数阈值
  static const int _speechFramesThreshold = 3; // 连续语音帧数阈值
  static const int _hangoverFrames = 5; // 语音结束后的延迟帧数
  
  // 状态管理
  bool _isActive = false;
  int _silenceFrameCount = 0;
  int _speechFrameCount = 0;
  int _hangoverFrameCount = 0;
  
  // 统计信息
  int _totalFrames = 0;
  int _activeFrames = 0;
  int _silentFrames = 0;
  DateTime? _lastActivityTime;
  DateTime? _sessionStartTime;

  /// 当前是否检测到语音活动
  bool get isActive => _isActive;
  
  /// 静音持续时间（秒）
  double get silenceDuration {
    if (_lastActivityTime == null) return 0.0;
    return DateTime.now().difference(_lastActivityTime!).inMilliseconds / 1000.0;
  }
  
  /// 活动检测统计
  Map<String, dynamic> get stats {
    final sessionDuration = _sessionStartTime != null
        ? DateTime.now().difference(_sessionStartTime!).inMilliseconds / 1000.0
        : 0.0;
    
    return {
      'totalFrames': _totalFrames,
      'activeFrames': _activeFrames,
      'silentFrames': _silentFrames,
      'activityRatio': _totalFrames > 0 ? _activeFrames / _totalFrames : 0.0,
      'sessionDuration': sessionDuration,
      'lastActivityTime': _lastActivityTime?.toString(),
      'currentSilenceDuration': silenceDuration,
    };
  }

  /// 开始新的检测会话
  void startSession() {
    _sessionStartTime = DateTime.now();
    _lastActivityTime = DateTime.now();
    _totalFrames = 0;
    _activeFrames = 0;
    _silentFrames = 0;
    _silenceFrameCount = 0;
    _speechFrameCount = 0;
    _hangoverFrameCount = 0;
    _isActive = false;
    
    debugPrint('音频活动检测会话开始');
  }

  /// 结束检测会话
  void endSession() {
    _sessionStartTime = null;
    _isActive = false;
    debugPrint('音频活动检测会话结束');
  }

  /// 检测音频帧中的语音活动
  bool detectActivity(Uint8List audioData) {
    if (_sessionStartTime == null) {
      startSession();
    }

    _totalFrames++;
    
    // 计算音频能量
    final energy = _calculateAudioEnergy(audioData);
    final hasVoice = energy > _silenceThreshold;
    
    // 状态机逻辑
    if (hasVoice) {
      _speechFrameCount++;
      _silenceFrameCount = 0;
      
      // 检测到语音活动
      if (!_isActive && _speechFrameCount >= _speechFramesThreshold) {
        _isActive = true;
        _lastActivityTime = DateTime.now();
        debugPrint('检测到语音活动开始 (能量: ${energy.toStringAsFixed(4)})');
      }
      
      if (_isActive) {
        _activeFrames++;
        _lastActivityTime = DateTime.now();
        _hangoverFrameCount = _hangoverFrames; // 重置延迟计数
      }
    } else {
      _speechFrameCount = 0;
      _silenceFrameCount++;
      _silentFrames++;
      
      // 处理延迟期
      if (_isActive && _hangoverFrameCount > 0) {
        _hangoverFrameCount--;
        _activeFrames++; // 延迟期仍然算作活动
      } else if (_isActive && _silenceFrameCount >= _silenceFramesThreshold) {
        // 检测到语音活动结束
        _isActive = false;
        debugPrint('检测到语音活动结束 (静音帧数: $_silenceFrameCount)');
      }
    }
    
    return _isActive;
  }

  /// 计算音频能量
  double _calculateAudioEnergy(Uint8List audioData) {
    if (audioData.isEmpty) return 0.0;
    
    double sum = 0.0;
    
    // 假设是16位PCM数据
    for (int i = 0; i < audioData.length - 1; i += 2) {
      // 将两个字节组合成16位整数
      final sample = (audioData[i + 1] << 8) | audioData[i];
      // 转换为有符号整数
      final signedSample = sample > 32767 ? sample - 65536 : sample;
      // 计算平方和
      sum += (signedSample / 32768.0) * (signedSample / 32768.0);
    }
    
    // 返回RMS值
    final frameCount = audioData.length ~/ 2;
    return frameCount > 0 ? sqrt(sum / frameCount) : 0.0;
  }

  /// 强制设置活动状态（用于调试或特殊情况）
  void forceActivity(bool active) {
    _isActive = active;
    if (active) {
      _lastActivityTime = DateTime.now();
    }
    debugPrint('强制设置音频活动状态: $active');
  }

  /// 重置检测器状态
  void reset() {
    _isActive = false;
    _silenceFrameCount = 0;
    _speechFrameCount = 0;
    _hangoverFrameCount = 0;
    debugPrint('音频活动检测器已重置');
  }

  /// 获取详细的调试信息
  String getDebugInfo() {
    return '''
音频活动检测器状态:
- 当前活动状态: $_isActive
- 静音帧计数: $_silenceFrameCount
- 语音帧计数: $_speechFrameCount
- 延迟帧计数: $_hangoverFrameCount
- 总帧数: $_totalFrames
- 活动帧数: $_activeFrames
- 静音帧数: $_silentFrames
- 活动比例: ${(_totalFrames > 0 ? _activeFrames / _totalFrames * 100 : 0).toStringAsFixed(1)}%
- 当前静音时长: ${silenceDuration.toStringAsFixed(1)}秒
''';
  }
}

/// 音频活动检测配置
class AudioActivityConfig {
  /// 静音阈值 (0.0-1.0)
  final double silenceThreshold;
  
  /// 连续静音帧数阈值
  final int silenceFramesThreshold;
  
  /// 连续语音帧数阈值
  final int speechFramesThreshold;
  
  /// 语音结束后的延迟帧数
  final int hangoverFrames;
  
  /// 最大静音时长（秒），超过此时长自动停止STT
  final double maxSilenceDuration;

  const AudioActivityConfig({
    this.silenceThreshold = 0.01,
    this.silenceFramesThreshold = 10,
    this.speechFramesThreshold = 3,
    this.hangoverFrames = 5,
    this.maxSilenceDuration = 10.0,
  });

  /// 创建敏感配置（更容易检测到语音）
  factory AudioActivityConfig.sensitive() {
    return const AudioActivityConfig(
      silenceThreshold: 0.005,
      silenceFramesThreshold: 5,
      speechFramesThreshold: 2,
      hangoverFrames: 8,
      maxSilenceDuration: 15.0,
    );
  }

  /// 创建保守配置（减少误检测）
  factory AudioActivityConfig.conservative() {
    return const AudioActivityConfig(
      silenceThreshold: 0.02,
      silenceFramesThreshold: 15,
      speechFramesThreshold: 5,
      hangoverFrames: 3,
      maxSilenceDuration: 5.0,
    );
  }

  /// 创建节省配置（最大化成本节省）
  factory AudioActivityConfig.costSaving() {
    return const AudioActivityConfig(
      silenceThreshold: 0.015,
      silenceFramesThreshold: 8,
      speechFramesThreshold: 4,
      hangoverFrames: 2,
      maxSilenceDuration: 3.0,
    );
  }
}
