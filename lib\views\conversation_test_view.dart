import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../services/deepseek_conversation_service.dart';

class ConversationTestView extends HookWidget {
  const ConversationTestView({super.key});

  @override
  Widget build(BuildContext context) {
    final textController = useTextEditingController();
    final responseState = useState<String>('');
    final isLoading = useState<bool>(false);
    final apiKeyController = useTextEditingController();
    final isApiKeyConfigured = useState<bool>(false);

    final conversationService = useMemoized(
      () => DeepSeekConversationService(const FlutterSecureStorage()),
      [],
    );

    // 检查API密钥状态
    useEffect(() {
      conversationService.isApiKeyConfigured().then((configured) {
        isApiKeyConfigured.value = configured;
      });
      return null;
    }, []);

    Future<void> setApiKey() async {
      if (apiKeyController.text.trim().isNotEmpty) {
        await conversationService.setApiKey(apiKeyController.text.trim());
        isApiKeyConfigured.value =
            await conversationService.isApiKeyConfigured();
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('API密钥已保存'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    }

    Future<void> sendMessage() async {
      if (textController.text.trim().isEmpty) return;

      isLoading.value = true;
      responseState.value = '';

      try {
        final response = await conversationService.getEmpathicResponse(
          textController.text.trim(),
        );
        responseState.value = response;
      } catch (e) {
        responseState.value = '发生错误: $e';
      } finally {
        isLoading.value = false;
      }
    }

    Future<void> testConnection() async {
      isLoading.value = true;
      try {
        final success = await conversationService.testConnection();
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(success ? 'API连接成功！' : 'API连接失败'),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('连接测试失败: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: AppBar(
        title: const Text(
          'AI情感陪伴测试',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF2A2A2A),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // API密钥配置部分
            if (!isApiKeyConfigured.value) ...[
              const Text(
                'DeepSeek API密钥配置',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: apiKeyController,
                decoration: const InputDecoration(
                  labelText: 'DeepSeek API Key',
                  labelStyle: TextStyle(color: Colors.white70),
                  hintText: '请输入您的DeepSeek API密钥',
                  hintStyle: TextStyle(color: Colors.white30),
                  border: OutlineInputBorder(),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.orange),
                  ),
                ),
                style: const TextStyle(color: Colors.white),
                obscureText: true,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: setApiKey,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('保存API密钥'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: testConnection,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('测试连接'),
                  ),
                ],
              ),
              const SizedBox(height: 24),
            ],

            // 对话测试部分
            if (isApiKeyConfigured.value) ...[
              const Text(
                '情感陪伴测试',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),

              // 用户输入
              TextField(
                controller: textController,
                maxLines: 4,
                decoration: const InputDecoration(
                  labelText: '分享你的感受...',
                  labelStyle: TextStyle(color: Colors.white70),
                  hintText: '例如：今天感觉特别累，工作压力很大...',
                  hintStyle: TextStyle(color: Colors.white30),
                  border: OutlineInputBorder(),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.orange),
                  ),
                  alignLabelWithHint: true,
                ),
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 16),

              // 发送按钮
              ElevatedButton(
                onPressed: isLoading.value ? null : sendMessage,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: isLoading.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        '获取AI回复',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
              const SizedBox(height: 24),

              // AI回复显示
              const Text(
                'AI的陪伴回复:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                constraints: const BoxConstraints(minHeight: 100),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2A2A),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.white30),
                ),
                child: Text(
                  responseState.value.isEmpty
                      ? '等待AI回复...'
                      : responseState.value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
              ),

              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: testConnection,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('测试API连接'),
              ),
            ],

            // 示例消息
            const Spacer(),
            const Text(
              '测试示例消息：',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildExampleChip('今天工作压力好大，感觉快撑不下去了', textController),
                _buildExampleChip('我感到很孤独，没人理解我', textController),
                _buildExampleChip('最近总是失眠，心情很烦躁', textController),
                _buildExampleChip('今天终于完成了一个重要项目，很开心', textController),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExampleChip(String text, TextEditingController controller) {
    return ActionChip(
      label: Text(
        text,
        style: const TextStyle(fontSize: 12, color: Colors.white),
      ),
      backgroundColor: const Color(0xFF2A2A2A),
      side: const BorderSide(color: Colors.white30),
      onPressed: () {
        controller.text = text;
      },
    );
  }
}
