import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../tts_service.dart';
import 'emotion_models.dart';

/// 共鸣AI服务
/// 
/// 负责根据情感分析和触发条件生成合适的AI共鸣回应
class EmpathyAiService extends ChangeNotifier {
  // DeepSeek API配置
  static String get _apiKey => dotenv.env['DEEPSEEK_API_KEY'] ?? '';
  static String get _baseUrl => dotenv.env['DEEPSEEK_BASE_URL'] ?? 'https://api.deepseek.com';
  
  // 服务依赖
  final TTSService _ttsService;
  
  // 状态管理
  bool _isGenerating = false;
  String? _lastResponse;
  DateTime? _lastResponseTime;
  
  // 事件流
  final StreamController<String> _responseController = 
      StreamController<String>.broadcast();
  
  // 共鸣回应模板
  static const Map<EmpathyResponseType, List<String>> _responseTemplates = {
    EmpathyResponseType.understanding: [
      '我能理解你现在的感受',
      '听起来你正在经历一些困难',
      '你的感受是完全可以理解的',
      '我听到了你内心的声音',
    ],
    EmpathyResponseType.emotionalSupport: [
      '你不是一个人在面对这些',
      '我会陪着你度过这段时间',
      '你已经很勇敢了',
      '这种感受会慢慢好起来的',
    ],
    EmpathyResponseType.gentleInquiry: [
      '你想多说一些吗？',
      '发生了什么让你有这样的感受？',
      '你现在最需要的是什么？',
      '能告诉我更多吗？',
    ],
    EmpathyResponseType.companionship: [
      '我在这里陪着你',
      '你不用一个人承受',
      '我会静静地听你说',
      '慢慢来，不用着急',
    ],
    EmpathyResponseType.encouragement: [
      '你比你想象的更坚强',
      '每一步都是进步',
      '你已经做得很好了',
      '相信自己，你可以的',
    ],
    EmpathyResponseType.silentPresence: [
      '...',
      '我在这里',
      '嗯',
      '我听着',
    ],
  };

  EmpathyAiService({required TTSService ttsService}) : _ttsService = ttsService;

  /// 是否正在生成回应
  bool get isGenerating => _isGenerating;
  
  /// 最后的回应
  String? get lastResponse => _lastResponse;
  
  /// 回应事件流
  Stream<String> get onResponse => _responseController.stream;

  /// 根据触发条件生成共鸣回应
  Future<String?> generateEmpathyResponse(EmpathyTrigger trigger) async {
    if (_isGenerating) {
      debugPrint('AI正在生成回应中，跳过新的触发');
      return null;
    }

    try {
      _isGenerating = true;
      notifyListeners();

      String response;
      
      // 根据触发强度选择回应策略
      if (trigger.strength > 0.8) {
        // 高强度触发：使用AI生成个性化回应
        response = await _generateAiResponse(trigger);
      } else if (trigger.strength > 0.5) {
        // 中等强度触发：使用模板回应
        response = _generateTemplateResponse(trigger.suggestedResponse);
      } else {
        // 低强度触发：简单的陪伴回应
        response = _generateSimpleResponse();
      }

      _lastResponse = response;
      _lastResponseTime = DateTime.now();
      
      // 发送回应事件
      _responseController.add(response);
      
      // 如果回应不是静默的，使用TTS播放
      if (response.trim() != '...' && response.trim() != '我在这里') {
        await _speakResponse(response);
      }

      debugPrint('生成共鸣回应: $response');
      return response;

    } catch (e) {
      debugPrint('生成共鸣回应失败: $e');
      return null;
    } finally {
      _isGenerating = false;
      notifyListeners();
    }
  }

  /// 使用AI生成个性化回应
  Future<String> _generateAiResponse(EmpathyTrigger trigger) async {
    try {
      // 构建上下文提示
      final contextPrompt = _buildContextPrompt(trigger);
      
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'deepseek-chat',
          'messages': [
            {
              'role': 'system',
              'content': _getSystemPrompt(),
            },
            {
              'role': 'user',
              'content': contextPrompt,
            },
          ],
          'max_tokens': 100,
          'temperature': 0.7,
          'stream': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final aiResponse = data['choices'][0]['message']['content'] as String;
        return aiResponse.trim();
      } else {
        debugPrint('AI API调用失败: ${response.statusCode}');
        return _generateTemplateResponse(trigger.suggestedResponse);
      }
    } catch (e) {
      debugPrint('AI回应生成异常: $e');
      return _generateTemplateResponse(trigger.suggestedResponse);
    }
  }

  /// 构建上下文提示
  String _buildContextPrompt(EmpathyTrigger trigger) {
    final buffer = StringBuffer();
    
    buffer.writeln('用户情况分析：');
    buffer.writeln('- 触发类型: ${_getTriggerTypeName(trigger.type)}');
    buffer.writeln('- 触发强度: ${(trigger.strength * 100).toStringAsFixed(0)}%');
    buffer.writeln('- 触发原因: ${trigger.reason}');
    
    if (trigger.emotionAnalysis != null) {
      final emotion = trigger.emotionAnalysis!;
      buffer.writeln('- 情感状态: ${_getEmotionName(emotion.primaryEmotion)}');
      buffer.writeln('- 情感强度: ${emotion.intensity.name}');
      buffer.writeln('- 用户说的话: "${emotion.originalText}"');
      if (emotion.keywords.isNotEmpty) {
        buffer.writeln('- 关键词: ${emotion.keywords.join(", ")}');
      }
    }
    
    if (trigger.speechFeatures != null) {
      final speech = trigger.speechFeatures!;
      buffer.writeln('- 语速: ${speech.speechRate.toStringAsFixed(1)} 字符/秒');
      if (speech.hasPause) {
        buffer.writeln('- 停顿时长: ${speech.pauseDuration.toStringAsFixed(1)} 秒');
      }
    }
    
    buffer.writeln('\n请生成一个简短、温暖的共鸣回应（1-2句话）：');
    
    return buffer.toString();
  }

  /// 获取系统提示词
  String _getSystemPrompt() {
    return '''
你是一个温和且富有同理心的AI倾听者，在"回声洞"应用中陪伴用户。

你的角色特点：
- 专注于情感陪伴和理解，不提供具体建议
- 回应简短温暖，通常1-2句话
- 使用温和、包容的语言
- 避免说教或试图解决问题
- 重点是让用户感受到被理解和陪伴

回应原则：
- 如果用户表达负面情感，给予理解和支持
- 如果用户停顿或犹豫，给予鼓励继续表达
- 如果用户重复表达，确认你听到了他们的感受
- 保持简洁，避免长篇回应
- 使用"我听到了"、"我理解"、"我陪着你"等表达

请根据用户的具体情况生成合适的共鸣回应。
''';
  }

  /// 生成模板回应
  String _generateTemplateResponse(EmpathyResponseType responseType) {
    final templates = _responseTemplates[responseType] ?? 
                     _responseTemplates[EmpathyResponseType.understanding]!;
    
    // 随机选择一个模板
    final randomIndex = DateTime.now().millisecondsSinceEpoch % templates.length;
    return templates[randomIndex];
  }

  /// 生成简单回应
  String _generateSimpleResponse() {
    const simpleResponses = [
      '嗯',
      '我听着',
      '我在这里',
      '继续说吧',
    ];
    
    final randomIndex = DateTime.now().millisecondsSinceEpoch % simpleResponses.length;
    return simpleResponses[randomIndex];
  }

  /// 使用TTS播放回应
  Future<void> _speakResponse(String response) async {
    try {
      await _ttsService.speak(response);
    } catch (e) {
      debugPrint('TTS播放失败: $e');
    }
  }

  /// 获取触发类型名称
  String _getTriggerTypeName(EmpathyTriggerType type) {
    const names = {
      EmpathyTriggerType.emotionalKeyword: '情感关键词',
      EmpathyTriggerType.speechRateAnomaly: '语速异常',
      EmpathyTriggerType.longPause: '长时间停顿',
      EmpathyTriggerType.emotionIntensity: '情感强度',
      EmpathyTriggerType.repetitiveExpression: '重复表达',
      EmpathyTriggerType.helpSignal: '求助信号',
      EmpathyTriggerType.emotionTransition: '情感转换',
    };
    return names[type] ?? type.name;
  }

  /// 获取情感名称
  String _getEmotionName(EmotionType emotion) {
    const names = {
      EmotionType.sad: '悲伤',
      EmotionType.anxious: '焦虑',
      EmotionType.angry: '愤怒',
      EmotionType.lonely: '孤独',
      EmotionType.stressed: '压力',
      EmotionType.confused: '困惑',
      EmotionType.happy: '快乐',
      EmotionType.hopeful: '希望',
      EmotionType.fearful: '恐惧',
      EmotionType.neutral: '平静',
      EmotionType.positive: '积极',
      EmotionType.negative: '消极',
    };
    return names[emotion] ?? emotion.name;
  }

  /// 检查是否应该回应
  bool shouldRespond(EmpathyTrigger trigger) {
    // 检查冷却期
    if (_lastResponseTime != null) {
      final timeSinceLastResponse = DateTime.now().difference(_lastResponseTime!);
      if (timeSinceLastResponse < const Duration(seconds: 5)) {
        return false;
      }
    }
    
    // 检查触发强度
    return trigger.strength > 0.4;
  }

  /// 获取回应统计信息
  Map<String, dynamic> getResponseStats() {
    return {
      'isGenerating': _isGenerating,
      'lastResponse': _lastResponse,
      'lastResponseTime': _lastResponseTime?.toString(),
      'hasApiKey': _apiKey.isNotEmpty,
    };
  }

  @override
  void dispose() {
    _responseController.close();
    super.dispose();
  }
}
