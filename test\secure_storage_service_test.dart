import 'package:flutter_test/flutter_test.dart';
import 'package:echo_cave/data/secure_storage_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Mock FlutterSecureStorage for testing
class MockFlutterSecureStorage extends Fake implements FlutterSecureStorage {
  final Map<String, String> _storage = {};

  @override
  Future<String?> read({
    required String key,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WindowsOptions? wOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
  }) async {
    return _storage[key];
  }

  @override
  Future<void> write({
    required String key,
    required String? value,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WindowsOptions? wOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
  }) async {
    if (value != null) {
      _storage[key] = value;
    } else {
      _storage.remove(key);
    }
  }
}

void main() {
  group('SecureStorageService', () {
    late SecureStorageService secureStorageService;
    late MockFlutterSecureStorage mockStorage;

    setUp(() {
      mockStorage = MockFlutterSecureStorage();
      // We inject the mock storage into our service.
      secureStorageService = SecureStorageService(mockStorage);
    });

    test('should generate and store a key on first use', () async {
      // Act: Call a method that triggers key creation.
      await secureStorageService.encryptData("test data");

      // Assert: Check that a key was written to the mock storage.
      final key = await mockStorage.read(key: 'audio_encryption_key');
      expect(key, isNotNull);
      expect(key, isA<String>());
    });

    test('should retrieve existing key on subsequent uses', () async {
      // Arrange: Manually write a valid, 32-byte Base64 encoded key.
      const testKey =
          'YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoxMjM0NTY='; // A valid 32-byte key
      await mockStorage.write(key: 'audio_encryption_key', value: testKey);

      // Act: Create a new service instance to simulate a new app session.
      final newSecureStorageService = SecureStorageService(mockStorage);
      // Trigger key retrieval.
      await newSecureStorageService.encryptData("test data");

      // Assert: Ensure the key was read and not overwritten.
      final retrievedKey = await mockStorage.read(key: 'audio_encryption_key');
      expect(retrievedKey, testKey);
    });

    test('encrypt and decrypt should return original text', () async {
      const originalText = 'Hello, Echo Cave!';

      final encryptedText =
          await secureStorageService.encryptData(originalText);
      final decryptedText =
          await secureStorageService.decryptData(encryptedText);

      expect(decryptedText, originalText);
      expect(encryptedText, isNot(equals(originalText)));
    });
  });
}
